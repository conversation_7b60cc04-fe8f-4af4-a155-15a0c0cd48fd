#!/usr/bin/env python3
"""
LEGO城堡搭建系统实时演示
展示完整的搭建流程和系统功能

作者: AI Assistant
日期: 2025-01-26
版本: 1.0
"""

import sys
import os
import time
import json
import numpy as np

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入核心模块
from castle_structure import CastleStructureDefinition
from yumi_controller import YuMiDualArmController
from collision_detection import CollisionDetector
from stability_analyzer import StabilityAnalyzer
from visualization import CastleVisualizer

def print_header(title):
    """打印标题"""
    print("\n" + "🏰" + "=" * 58 + "🏰")
    print(f"    {title}")
    print("🏰" + "=" * 58 + "🏰")

def print_step(step_num, total_steps, description):
    """打印步骤信息"""
    print(f"\n📋 步骤 {step_num}/{total_steps}: {description}")
    print("-" * 50)

def simulate_build_step(step_info, yumi_controller, detector, analyzer):
    """模拟单个搭建步骤"""
    brick_id = step_info['brick_id']
    position = step_info['position']
    arm = step_info['arm']
    
    print(f"🤖 使用{arm}臂搭建积木: {brick_id}")
    print(f"   目标位置: ({position[0]:.3f}, {position[1]:.3f}, {position[2]:.3f})")
    
    # 1. 拾取积木
    supply_position = [0.3, -0.2 if arm == 'left' else 0.2, 0.05]
    print(f"   1. 从供应位置拾取积木...")
    pick_success = yumi_controller.pick_brick(arm, supply_position, "brick_2x4")
    
    if pick_success:
        print(f"      ✅ 拾取成功")
    else:
        print(f"      ❌ 拾取失败")
        return False
    
    # 2. 移动到目标位置
    print(f"   2. 移动到目标位置...")
    place_success = yumi_controller.place_brick(arm, position, 0)
    
    if place_success:
        print(f"      ✅ 放置成功")
    else:
        print(f"      ❌ 放置失败")
        return False
    
    # 3. 碰撞检测
    print(f"   3. 执行碰撞检测...")
    test_objects = {
        brick_id: {
            'position': position,
            'orientation': 0,
            'size': [0.0318, 0.0159, 0.0096]
        }
    }
    
    collisions = detector.check_collisions(test_objects)
    if len(collisions) == 0:
        print(f"      ✅ 无碰撞检测")
    else:
        print(f"      ⚠️ 检测到 {len(collisions)} 个碰撞")
    
    # 4. 稳定性分析
    print(f"   4. 分析结构稳定性...")
    test_structure = {
        brick_id: {
            'position': position,
            'level': step_info['level'],
            'mass': 0.00253,
            'size': [0.0318, 0.0159, 0.0096]
        }
    }
    
    stability_report = analyzer.analyze_complete_stability(test_structure)
    print(f"      稳定性评分: {stability_report.overall_score:.3f}")
    print(f"      稳定性评级: {stability_report.overall_rating.value}")
    
    return True

def main():
    """主演示函数"""
    print_header("LEGO城堡搭建系统 - 实时演示")
    
    print("🎯 演示目标:")
    print("   • 展示完整的搭建流程")
    print("   • 验证系统各模块功能")
    print("   • 模拟真实的机械臂操作")
    print("   • 实时监控搭建过程")
    
    # 步骤1: 初始化系统
    print_step(1, 6, "初始化系统组件")
    
    print("🏰 加载城堡结构...")
    castle = CastleStructureDefinition()
    print(f"   ✅ 城堡结构: {castle.get_total_brick_count()}个积木, {castle.get_level_count()}层")
    
    print("🤖 初始化YuMi控制器...")
    yumi_controller = YuMiDualArmController(
        ip_address="127.0.0.1",  # 演示模式
        left_enabled=True,
        right_enabled=True
    )
    print(f"   ✅ YuMi状态: 连接={yumi_controller.is_connected()}")
    
    print("🔍 初始化碰撞检测器...")
    detector = CollisionDetector(tolerance=1e-4)
    print(f"   ✅ 碰撞检测器: 容差={detector.tolerance}")
    
    print("⚖️ 初始化稳定性分析器...")
    analyzer = StabilityAnalyzer(threshold=0.7)
    print(f"   ✅ 稳定性分析器: 阈值={analyzer.threshold}")
    
    print("🎨 初始化可视化系统...")
    visualizer = CastleVisualizer(real_time=False)
    print(f"   ✅ 可视化系统: 引擎={visualizer.visualization_engine}")
    
    # 步骤2: 生成搭建计划
    print_step(2, 6, "生成搭建计划")
    
    # 选择前6个积木进行演示
    demo_bricks = []
    for level in range(1, 4):  # Level 1-3
        level_bricks = castle.get_level_bricks(level)
        for i, brick in enumerate(level_bricks[:2]):  # 每层取2个积木
            demo_bricks.append({
                'brick_id': brick['id'],
                'position': brick['position'],
                'level': level,
                'arm': 'left' if i % 2 == 0 else 'right'
            })
    
    print(f"📋 演示搭建计划:")
    for i, brick in enumerate(demo_bricks):
        print(f"   {i+1}. {brick['brick_id']} (Level {brick['level']}, {brick['arm']}臂)")
    
    # 步骤3: 工作空间验证
    print_step(3, 6, "工作空间验证")
    
    print("📐 验证积木位置是否在工作空间内...")
    workspace_valid = True
    
    for brick in demo_bricks:
        pos = brick['position']
        x, y, z = pos[0], pos[1], pos[2]
        
        # 检查工作空间限制
        if not (0.3 <= x <= 0.7 and -0.3 <= y <= 0.3 and 0.05 <= z <= 0.2):
            print(f"   ⚠️ {brick['brick_id']} 位置可能超出工作空间")
            workspace_valid = False
        else:
            print(f"   ✅ {brick['brick_id']} 位置验证通过")
    
    if workspace_valid:
        print("   ✅ 所有积木位置都在安全工作空间内")
    else:
        print("   ⚠️ 部分积木位置需要调整")
    
    # 步骤4: 执行搭建序列
    print_step(4, 6, "执行搭建序列")
    
    built_bricks = {}
    successful_steps = 0
    
    for i, brick_info in enumerate(demo_bricks):
        print(f"\n🔨 执行搭建步骤 {i+1}/{len(demo_bricks)}")
        
        # 模拟搭建步骤
        success = simulate_build_step(brick_info, yumi_controller, detector, analyzer)
        
        if success:
            successful_steps += 1
            built_bricks[brick_info['brick_id']] = brick_info
            
            # 添加到可视化
            visualizer.add_brick(
                brick_info['brick_id'],
                brick_info['position'],
                0,  # 朝向
                "brick_2x4",
                "tan"
            )
            
            # 更新进度
            progress = (i + 1) / len(demo_bricks) * 100
            visualizer.update_progress(
                level=brick_info['level'],
                completed=i + 1,
                total=len(demo_bricks)
            )
            
            print(f"      📊 总进度: {progress:.1f}%")
        else:
            print(f"      ❌ 步骤失败，继续下一步")
        
        # 短暂暂停以模拟真实搭建时间
        time.sleep(1)
    
    # 步骤5: 整体结构分析
    print_step(5, 6, "整体结构分析")
    
    print("🔍 分析已搭建的结构...")
    
    # 构建完整结构数据
    complete_structure = {}
    for brick_id, brick_info in built_bricks.items():
        complete_structure[brick_id] = {
            'position': brick_info['position'],
            'level': brick_info['level'],
            'mass': 0.00253,
            'size': [0.0318, 0.0159, 0.0096]
        }
    
    # 执行完整分析
    if complete_structure:
        print("⚖️ 执行稳定性分析...")
        final_report = analyzer.analyze_complete_stability(complete_structure)
        
        print(f"   📊 分析结果:")
        print(f"      总体评分: {final_report.overall_score:.3f}")
        print(f"      总体评级: {final_report.overall_rating.value}")
        print(f"      是否稳定: {'是' if final_report.is_stable else '否'}")
        
        print(f"   🔍 组件评分:")
        for component, score in final_report.component_scores.items():
            print(f"      {component}: {score:.3f}")
        
        if final_report.risk_factors:
            print(f"   ⚠️ 风险因素:")
            for risk in final_report.risk_factors:
                print(f"      • {risk}")
        
        print(f"   💡 建议:")
        for rec in final_report.recommendations:
            print(f"      • {rec}")
        
        # 添加重心标记
        com = final_report.com_analysis.system_com
        visualizer.add_center_of_mass([com[0], com[1], com[2]])
        
        # 添加支撑多边形
        if len(final_report.com_analysis.support_polygon) > 2:
            visualizer.add_support_polygon(final_report.com_analysis.support_polygon)
    
    # 步骤6: 生成报告和清理
    print_step(6, 6, "生成报告和清理")
    
    print("📊 生成搭建报告...")
    
    # 创建搭建报告
    build_report = {
        'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
        'demo_info': {
            'total_planned_bricks': len(demo_bricks),
            'successful_bricks': successful_steps,
            'success_rate': successful_steps / len(demo_bricks) * 100
        },
        'system_performance': {
            'yumi_controller': 'operational',
            'collision_detection': 'operational',
            'stability_analysis': 'operational',
            'visualization': 'operational'
        },
        'final_structure': {
            'brick_count': len(built_bricks),
            'stability_score': final_report.overall_score if 'final_report' in locals() else 0,
            'stability_rating': final_report.overall_rating.value if 'final_report' in locals() else 'unknown'
        }
    }
    
    # 保存报告
    with open('live_demo_report.json', 'w', encoding='utf-8') as f:
        json.dump(build_report, f, indent=2, ensure_ascii=False)
    
    print(f"   ✅ 搭建报告已保存: live_demo_report.json")
    
    # 保存可视化截图
    print("📸 保存可视化截图...")
    visualizer.save_screenshot('live_demo_result.png')
    print(f"   ✅ 截图已保存: live_demo_result.png")
    
    # 清理资源
    print("🧹 清理系统资源...")
    yumi_controller.disconnect()
    visualizer.close()
    print(f"   ✅ 资源清理完成")
    
    # 最终总结
    print_header("演示完成总结")
    
    print(f"🎉 LEGO城堡搭建系统实时演示完成！")
    print(f"\n📊 演示结果:")
    print(f"   计划搭建: {len(demo_bricks)} 个积木")
    print(f"   成功搭建: {successful_steps} 个积木")
    print(f"   成功率: {successful_steps/len(demo_bricks)*100:.1f}%")
    
    if 'final_report' in locals():
        print(f"   最终稳定性: {final_report.overall_score:.3f} ({final_report.overall_rating.value})")
    
    print(f"\n✅ 系统功能验证:")
    print(f"   🤖 YuMi双臂控制: 正常")
    print(f"   🔍 碰撞检测: 正常")
    print(f"   ⚖️ 稳定性分析: 正常")
    print(f"   🎨 3D可视化: 正常")
    
    print(f"\n📁 生成的文件:")
    print(f"   • live_demo_report.json - 搭建报告")
    print(f"   • live_demo_result.png - 可视化截图")
    
    print(f"\n🚀 系统已准备就绪，可以进行真实的LEGO城堡搭建！")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断演示")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 演示失败: {e}")
        sys.exit(1)
