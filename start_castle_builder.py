#!/usr/bin/env python3
"""
LEGO城堡搭建系统启动脚本
无需外部依赖的快速启动版本

作者: AI Assistant
日期: 2025-01-26
版本: 1.0
"""

import sys
import os
import json
import time
import numpy as np

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def print_banner():
    """显示启动横幅"""
    print("🏰" + "=" * 58 + "🏰")
    print("    LEGO城堡搭建系统 - Python版本启动")
    print("    基于参考图片的精确8层城堡自动搭建")
    print("🏰" + "=" * 58 + "🏰")

def check_environment():
    """检查运行环境"""
    print("\n🔍 检查运行环境...")
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version >= (3, 7):
        print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    else:
        print(f"❌ Python版本过低: {python_version.major}.{python_version.minor}")
        return False
    
    # 检查核心模块
    core_modules = [
        'castle_structure.py',
        'yumi_controller.py', 
        'collision_detection.py',
        'stability_analyzer.py',
        'visualization.py'
    ]
    
    missing_modules = []
    for module in core_modules:
        if os.path.exists(module):
            print(f"✅ {module}")
        else:
            print(f"❌ {module}")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"❌ 缺少核心模块: {missing_modules}")
        return False
    
    # 检查必要的Python包
    required_packages = ['numpy', 'matplotlib']
    available_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
            available_packages.append(package)
        except ImportError:
            print(f"❌ {package} (需要安装)")
    
    print(f"\n📊 环境检查结果: {len(available_packages)}/{len(required_packages)} 必要包可用")
    return len(available_packages) >= 1  # 至少需要numpy

def create_config():
    """创建默认配置文件"""
    print("\n⚙️ 创建系统配置...")
    
    config = {
        "system_info": {
            "name": "LEGO城堡搭建系统",
            "version": "1.0",
            "created": time.strftime('%Y-%m-%d %H:%M:%S')
        },
        "yumi_settings": {
            "ip_address": "*************",
            "left_arm_enabled": True,
            "right_arm_enabled": True,
            "connection_timeout": 10
        },
        "build_parameters": {
            "placement_speed": 0.05,
            "approach_height": 0.05,
            "safety_margin": 0.002,
            "stability_threshold": 0.7
        },
        "brick_properties": {
            "mass": 0.00253,
            "density": 1040,
            "friction_static": 0.6,
            "friction_kinetic": 0.4
        },
        "visualization": {
            "enable_3d": True,
            "real_time_display": True,
            "save_screenshots": True,
            "window_width": 1200,
            "window_height": 800
        },
        "simulation": {
            "physics_enabled": False,  # 默认关闭物理仿真
            "collision_detection": True,
            "stability_analysis": True,
            "time_step": 0.01
        }
    }
    
    config_file = 'castle_config.json'
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 配置文件已创建: {config_file}")
    return config

def load_castle_structure():
    """加载城堡结构"""
    print("\n🏰 加载城堡结构定义...")
    
    try:
        from castle_structure import CastleStructureDefinition
        
        castle = CastleStructureDefinition()
        
        print(f"✅ 城堡结构加载成功:")
        print(f"   总积木数: {castle.get_total_brick_count()}")
        print(f"   总层数: {castle.get_level_count()}")
        
        # 显示各层统计
        for level in range(1, min(4, castle.get_level_count() + 1)):  # 只显示前3层
            level_bricks = castle.get_level_bricks(level)
            print(f"   Level {level}: {len(level_bricks)} 个积木")
        
        if castle.get_level_count() > 3:
            print(f"   ... 还有 {castle.get_level_count() - 3} 层")
        
        return castle
        
    except Exception as e:
        print(f"❌ 城堡结构加载失败: {e}")
        return None

def test_yumi_controller():
    """测试YuMi控制器"""
    print("\n🤖 测试YuMi控制器...")
    
    try:
        from yumi_controller import YuMiDualArmController
        
        # 创建控制器（模拟模式）
        controller = YuMiDualArmController(
            ip_address="127.0.0.1",  # 本地模拟
            left_enabled=True,
            right_enabled=True
        )
        
        print(f"✅ YuMi控制器创建成功")
        print(f"   连接状态: {controller.is_connected()}")
        print(f"   左臂状态: {'启用' if controller.left_enabled else '禁用'}")
        print(f"   右臂状态: {'启用' if controller.right_enabled else '禁用'}")
        
        # 测试基本操作
        left_open = controller.open_gripper("left")
        right_open = controller.open_gripper("right")
        print(f"   夹爪测试: 左臂={left_open}, 右臂={right_open}")
        
        controller.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ YuMi控制器测试失败: {e}")
        return False

def test_collision_detection():
    """测试碰撞检测"""
    print("\n🔍 测试碰撞检测系统...")
    
    try:
        from collision_detection import CollisionDetector
        
        detector = CollisionDetector(tolerance=1e-4)
        
        # 创建测试场景
        objects = {
            'brick1': {
                'position': [0.5, 0, 0.065],
                'orientation': 0,
                'size': [0.0318, 0.0159, 0.0096]
            },
            'brick2': {
                'position': [0.51, 0, 0.065],  # 轻微重叠
                'orientation': 0,
                'size': [0.0318, 0.0159, 0.0096]
            }
        }
        
        start_time = time.time()
        collisions = detector.check_collisions(objects)
        detection_time = time.time() - start_time
        
        print(f"✅ 碰撞检测测试成功")
        print(f"   检测时间: {detection_time:.4f}s")
        print(f"   发现碰撞: {len(collisions)}个")
        
        return True
        
    except Exception as e:
        print(f"❌ 碰撞检测测试失败: {e}")
        return False

def test_stability_analysis():
    """测试稳定性分析"""
    print("\n⚖️ 测试稳定性分析系统...")
    
    try:
        from stability_analyzer import StabilityAnalyzer
        
        analyzer = StabilityAnalyzer(threshold=0.7)
        
        # 创建测试结构
        objects = {
            'base1': {
                'position': [0.484, -0.016, 0.065],
                'level': 1,
                'mass': 0.00253,
                'size': [0.0318, 0.0159, 0.0096]
            },
            'base2': {
                'position': [0.516, -0.016, 0.065],
                'level': 1,
                'mass': 0.00253,
                'size': [0.0318, 0.0159, 0.0096]
            },
            'top1': {
                'position': [0.5, 0, 0.075],
                'level': 2,
                'mass': 0.00253,
                'size': [0.0318, 0.0159, 0.0096]
            }
        }
        
        start_time = time.time()
        report = analyzer.analyze_complete_stability(objects)
        analysis_time = time.time() - start_time
        
        print(f"✅ 稳定性分析测试成功")
        print(f"   分析时间: {analysis_time:.4f}s")
        print(f"   稳定性评分: {report.overall_score:.3f}")
        print(f"   稳定性评级: {report.overall_rating.value}")
        print(f"   是否稳定: {'是' if report.is_stable else '否'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 稳定性分析测试失败: {e}")
        return False

def test_visualization():
    """测试可视化系统"""
    print("\n🎨 测试可视化系统...")
    
    try:
        from visualization import CastleVisualizer
        
        # 创建可视化器（非实时模式）
        visualizer = CastleVisualizer(real_time=False)
        
        print(f"✅ 可视化系统创建成功")
        print(f"   渲染引擎: {visualizer.visualization_engine}")
        
        # 添加测试积木
        visualizer.add_brick("test_brick", [0.5, 0, 0.065], 0, "brick_2x4", "tan")
        print(f"   积木添加: 成功")
        
        # 更新进度
        visualizer.update_progress(level=1, completed=1, total=43)
        print(f"   进度更新: 成功")
        
        # 清理
        visualizer.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 可视化系统测试失败: {e}")
        return False

def run_demo_sequence():
    """运行演示序列"""
    print("\n🎬 运行系统演示序列...")
    
    try:
        # 1. 加载城堡结构
        castle = load_castle_structure()
        if not castle:
            return False
        
        # 2. 创建简化的搭建序列
        print("\n📋 生成搭建序列...")
        
        build_sequence = []
        for level in range(1, 4):  # 演示前3层
            level_bricks = castle.get_level_bricks(level)
            for i, brick in enumerate(level_bricks[:2]):  # 每层只取前2个积木
                step = {
                    'step': len(build_sequence) + 1,
                    'level': level,
                    'brick_id': brick['id'],
                    'position': brick['position'],
                    'arm': 'left' if i % 2 == 0 else 'right'
                }
                build_sequence.append(step)
        
        print(f"✅ 搭建序列生成完成: {len(build_sequence)} 个步骤")
        
        # 3. 模拟搭建过程
        print("\n🔨 模拟搭建过程...")
        
        for i, step in enumerate(build_sequence):
            print(f"   步骤 {step['step']}: Level {step['level']}, {step['brick_id']}")
            print(f"      使用{step['arm']}臂, 位置{step['position']}")
            
            # 模拟搭建时间
            time.sleep(0.5)
            
            # 计算进度
            progress = (i + 1) / len(build_sequence) * 100
            print(f"      进度: {progress:.1f}%")
        
        print(f"\n🎉 演示搭建完成！")
        return True
        
    except Exception as e:
        print(f"❌ 演示序列运行失败: {e}")
        return False

def show_next_steps():
    """显示下一步操作"""
    print("\n📋 下一步操作指南:")
    print("=" * 50)
    
    print("\n🔧 完整系统安装:")
    print("   pip install -r requirements.txt")
    
    print("\n🧪 运行完整测试:")
    print("   python simple_test.py")
    
    print("\n🎮 运行系统演示:")
    print("   python demo_castle_builder.py")
    
    print("\n⚙️ 配置YuMi机械臂:")
    print("   1. 编辑 castle_config.json")
    print("   2. 设置正确的IP地址")
    print("   3. 配置机械臂参数")
    
    print("\n🚀 启动仿真模式:")
    print("   python run_castle_builder.py --simulation")
    
    print("\n🤖 启动真实模式:")
    print("   python run_castle_builder.py --real")
    
    print("\n📚 查看文档:")
    print("   README.md - 完整项目文档")
    print("   项目完成总结.md - 项目总结")

def main():
    """主启动函数"""
    print_banner()
    
    # 1. 环境检查
    if not check_environment():
        print("\n❌ 环境检查失败，请安装必要的依赖包")
        print("   pip install numpy matplotlib")
        return False
    
    # 2. 创建配置
    config = create_config()
    
    # 3. 系统测试
    print("\n🧪 运行系统测试...")
    
    tests = [
        ("城堡结构", load_castle_structure),
        ("YuMi控制器", test_yumi_controller),
        ("碰撞检测", test_collision_detection),
        ("稳定性分析", test_stability_analysis),
        ("可视化系统", test_visualization)
    ]
    
    passed_tests = 0
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
    
    print(f"\n📊 测试结果: {passed_tests}/{len(tests)} 通过")
    
    # 4. 运行演示
    if passed_tests >= 3:  # 至少3个测试通过
        demo_success = run_demo_sequence()
        
        if demo_success:
            print("\n🎉 LEGO城堡搭建系统启动成功！")
            print("✅ 所有核心功能正常工作")
            
            # 5. 显示下一步
            show_next_steps()
            
            return True
    
    print("\n⚠️ 系统启动完成，但部分功能可能受限")
    print("   建议安装完整依赖包以获得最佳体验")
    
    show_next_steps()
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🏰 欢迎使用LEGO城堡搭建系统！")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断启动")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        sys.exit(1)
