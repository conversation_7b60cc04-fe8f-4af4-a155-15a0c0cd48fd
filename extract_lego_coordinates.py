#!/usr/bin/env python3
"""
LEGO积木坐标提取脚本
专门提取Excel文件中LEGO积木的坐标数据

作者: AI Assistant
日期: 2025-07-27
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

def extract_coordinates_from_excel(file_path):
    """
    从特定结构的Excel文件中提取LEGO积木坐标
    文件结构:
    - 第1行: 标题行 (初始位置/末位置)
    - 第2行: 列名 (Brick ID, Type, Level, X, Y, Z)
    - 第3行开始: 数据
    """
    try:
        # 直接读取Excel文件，不指定header
        df_raw = pd.read_excel(file_path, header=None)
        print(f"✅ 成功读取: {file_path}")
        print(f"  文件包含 {len(df_raw)} 行，{len(df_raw.columns)} 列数据")
        
        # 从第2行找到列名
        header_row = 1  # 对应Excel中的第2行
        columns = df_raw.iloc[header_row].tolist()
        
        # 打印调试信息
        print("\n📑 文件结构:")
        print(f"  表头行 (第1行): {df_raw.iloc[0].tolist()}")
        print(f"  列名行 (第2行): {columns}")
        
        # 查找坐标列的位置
        coord_columns = []
        for i, col in enumerate(columns):
            if str(col).lower() == 'x (m)':
                coord_columns.append(('X', i))
            elif str(col).lower() == 'y (m)':
                coord_columns.append(('Y', i))
            elif str(col).lower() == 'z (m)':
                coord_columns.append(('Z', i))
        
        print(f"  识别到的坐标列: {coord_columns}")
        
        # 如果没找到坐标列，可能是格式不标准
        if not coord_columns:
            print("  未找到标准坐标列，尝试使用第3-5列和第11-13列")
            coord_columns = [('X1', 3), ('Y1', 4), ('Z1', 5), ('X2', 11), ('Y2', 12), ('Z2', 13)]
        
        # 从第3行开始提取数据
        data_start_row = 2  # 对应Excel中的第3行
        data_rows = df_raw.iloc[data_start_row:]
        
        # 创建结果DataFrame
        result = {
            'Brick_ID': [],
            'Type': [],
            'Level': [],
            'X': [],
            'Y': [],
            'Z': []
        }
        
        # 处理数据行
        for idx, row in data_rows.iterrows():
            # 提取初始位置的坐标
            if not pd.isna(row[3]) and not pd.isna(row[4]) and not pd.isna(row[5]):
                try:
                    brick_id = row[0] if not pd.isna(row[0]) else f"Brick_{idx}"
                    brick_type = row[1] if not pd.isna(row[1]) else "Unknown"
                    level = int(row[2]) if not pd.isna(row[2]) else 1
                    
                    result['Brick_ID'].append(f"{brick_id}_start")
                    result['Type'].append(brick_type)
                    result['Level'].append(level)
                    result['X'].append(float(row[3]))
                    result['Y'].append(float(row[4]))
                    result['Z'].append(float(row[5]))
                except (ValueError, TypeError) as e:
                    print(f"  警告: 无法处理起始位置 (行 {idx+1}): {e}")
            
            # 提取末位置的坐标
            if not pd.isna(row[11]) and not pd.isna(row[12]) and not pd.isna(row[13]):
                try:
                    brick_id = row[8] if not pd.isna(row[8]) else (row[0] if not pd.isna(row[0]) else f"Brick_{idx}")
                    brick_type = row[9] if not pd.isna(row[9]) else (row[1] if not pd.isna(row[1]) else "Unknown")
                    level = int(row[10]) if not pd.isna(row[10]) else (int(row[2]) if not pd.isna(row[2]) else 1)
                    
                    result['Brick_ID'].append(f"{brick_id}_end")
                    result['Type'].append(brick_type)
                    result['Level'].append(level)
                    result['X'].append(float(row[11]))
                    result['Y'].append(float(row[12]))
                    result['Z'].append(float(row[13]))
                except (ValueError, TypeError) as e:
                    print(f"  警告: 无法处理结束位置 (行 {idx+1}): {e}")
        
        # 转换为DataFrame
        result_df = pd.DataFrame(result)
        
        print(f"\n✅ 成功提取 {len(result_df)} 个坐标点")
        if len(result_df) > 0:
            print("\n📊 坐标点示例:")
            print(result_df.head().to_string())
            
            print("\n📈 坐标范围:")
            print(f"  X: {result_df['X'].min():.4f} 到 {result_df['X'].max():.4f}")
            print(f"  Y: {result_df['Y'].min():.4f} 到 {result_df['Y'].max():.4f}")
            print(f"  Z: {result_df['Z'].min():.4f} 到 {result_df['Z'].max():.4f}")
            
            # 按层级统计积木数
            level_counts = result_df.groupby('Level').size()
            print("\n🏗️ 层级统计:")
            for level, count in level_counts.items():
                print(f"  第 {level} 层: {count//2} 个积木")  # 除以2因为每个积木有起始和结束位置
                
        return result_df
    
    except Exception as e:
        print(f"❌ 提取坐标时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame()

def visualize_coordinates(df, title, save_path):
    """可视化积木坐标"""
    if len(df) == 0:
        print("❌ 没有可视化的坐标数据")
        return False
    
    try:
        # 创建图形
        fig = plt.figure(figsize=(12, 10))
        ax = fig.add_subplot(111, projection='3d')
        
        # 区分起始位置和结束位置
        start_positions = df[df['Brick_ID'].str.contains('_start')]
        end_positions = df[df['Brick_ID'].str.contains('_end')]
        
        # 绘制起始位置 (红色)
        ax.scatter(start_positions['X'], start_positions['Y'], start_positions['Z'],
                  c='red', marker='o', s=50, alpha=0.7, label='起始位置')
        
        # 绘制结束位置 (蓝色)
        ax.scatter(end_positions['X'], end_positions['Y'], end_positions['Z'],
                  c='blue', marker='s', s=70, alpha=0.7, label='结束位置')
        
        # 为相同积木的起始和结束位置添加连接线
        for brick_id in start_positions['Brick_ID'].str.replace('_start', '').unique():
            start = start_positions[start_positions['Brick_ID'] == f"{brick_id}_start"]
            end = end_positions[end_positions['Brick_ID'] == f"{brick_id}_end"]
            
            if len(start) == 1 and len(end) == 1:
                ax.plot([start['X'].values[0], end['X'].values[0]],
                        [start['Y'].values[0], end['Y'].values[0]],
                        [start['Z'].values[0], end['Z'].values[0]],
                        'k-', alpha=0.3)
        
        # 按Level值着色绘制结束位置的散点图 (这是积木的最终堆叠位置)
        scatter = ax.scatter(end_positions['X'], end_positions['Y'], end_positions['Z'],
                            c=end_positions['Level'], cmap='viridis',
                            marker='o', s=100, alpha=1.0)
        
        # 添加颜色条 (显示Level)
        cbar = plt.colorbar(scatter)
        cbar.set_label('层级 (Level)')
        
        # 设置坐标轴标签
        ax.set_xlabel('X 坐标 (m)')
        ax.set_ylabel('Y 坐标 (m)')
        ax.set_zlabel('Z 坐标 (m)')
        
        # 设置标题
        ax.set_title(f'LEGO积木坐标可视化 - {title}')
        
        # 添加图例
        ax.legend()
        
        # 调整视角
        ax.view_init(elev=35, azim=45)
        
        # 保存图像
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"✅ 可视化图像已保存到: {save_path}")
        
        # 另一个角度
        ax.view_init(elev=0, azim=0)
        save_path_side = save_path.replace('.png', '_side.png')
        plt.savefig(save_path_side, dpi=300, bbox_inches='tight')
        print(f"✅ 侧视图已保存到: {save_path_side}")
        
        # 俯视图
        ax.view_init(elev=90, azim=0)
        save_path_top = save_path.replace('.png', '_top.png')
        plt.savefig(save_path_top, dpi=300, bbox_inches='tight')
        print(f"✅ 俯视图已保存到: {save_path_top}")
        
        plt.close()
        return True
    
    except Exception as e:
        print(f"❌ 可视化过程出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def export_to_csv(df, output_path):
    """导出坐标点到CSV文件"""
    if len(df) == 0:
        print("❌ 没有数据可导出")
        return False
    
    try:
        df.to_csv(output_path, index=False)
        print(f"✅ 坐标数据已导出至: {output_path}")
        return True
    except Exception as e:
        print(f"❌ 导出CSV时出错: {str(e)}")
        return False

def main():
    """主函数"""
    print("🧩 LEGO积木坐标提取工具")
    print("=" * 50)
    
    # 定义Excel文件路径
    excel_files = [
        '说明/積木座標.csv.xlsx',
        '说明/第一層積木.csv.xlsx',
    ]
    
    for file_path in excel_files:
        if os.path.exists(file_path):
            print(f"\n📄 处理文件: {file_path}")
            print("-" * 40)
            
            # 提取坐标
            df = extract_coordinates_from_excel(file_path)
            
            if len(df) > 0:
                # 导出为CSV
                output_csv = f"extracted_{os.path.basename(file_path).replace('.xlsx', '.csv')}"
                export_to_csv(df, output_csv)
                
                # 可视化
                output_image = f"lego_coordinates_{os.path.basename(file_path).split('.')[0]}.png"
                visualize_coordinates(df, os.path.basename(file_path), output_image)
            
            print("-" * 40)
        else:
            print(f"\n❌ 文件不存在: {file_path}")
    
    print("\n✅ 处理完成")

if __name__ == "__main__":
    main() 