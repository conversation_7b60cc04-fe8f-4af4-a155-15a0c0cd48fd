#!/usr/bin/env python3
"""
LEGO城堡搭建系统主运行脚本
基于参考图片实现精确的8层城堡搭建

作者: AI Assistant
日期: 2025-01-26
版本: 1.0
"""

import sys
import os
import argparse
import logging
import json
import time
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入主要模块
from lego_castle_builder import LegoCastleBuilder, BuildConfig, BuildStatus
from castle_structure import CastleStructureDefinition
from visualization import CastleVisualizer

def setup_logging(log_level: str = "INFO", log_file: str = None):
    """设置日志系统"""
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    handlers = [logging.StreamHandler()]
    if log_file:
        handlers.append(logging.FileHandler(log_file))
    
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format=log_format,
        handlers=handlers
    )

def load_config(config_file: str) -> BuildConfig:
    """加载配置文件"""
    if not os.path.exists(config_file):
        print(f"配置文件不存在: {config_file}")
        print("使用默认配置")
        return BuildConfig()
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        # 创建配置对象
        config = BuildConfig()
        
        # 更新配置参数
        for key, value in config_data.items():
            if hasattr(config, key):
                setattr(config, key, value)
        
        print(f"✅ 配置文件加载成功: {config_file}")
        return config
        
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")
        print("使用默认配置")
        return BuildConfig()

def create_default_config(config_file: str):
    """创建默认配置文件"""
    default_config = {
        "yumi_ip": "*************",
        "left_arm_enabled": True,
        "right_arm_enabled": True,
        "brick_mass": 0.00253,
        "brick_density": 1040,
        "friction_static": 0.6,
        "friction_kinetic": 0.4,
        "placement_speed": 0.05,
        "approach_height": 0.05,
        "safety_margin": 0.002,
        "stability_threshold": 0.7,
        "collision_tolerance": 1e-4,
        "enable_visualization": True,
        "real_time_display": True,
        "save_progress": True
    }
    
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 默认配置文件已创建: {config_file}")
        
    except Exception as e:
        print(f"❌ 创建配置文件失败: {e}")

def validate_system():
    """验证系统环境"""
    print("🔍 验证系统环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ Python版本过低，需要Python 3.7+")
        return False
    
    # 检查必要的包
    required_packages = [
        'numpy', 'matplotlib', 'scipy'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少必要的包: {', '.join(missing_packages)}")
        print("请运行: pip install numpy matplotlib scipy")
        return False
    
    # 检查可选包
    optional_packages = {
        'pybullet': '物理仿真',
        'open3d': '3D可视化',
        'cv2': '视觉处理'
    }
    
    for package, description in optional_packages.items():
        try:
            __import__(package)
            print(f"✅ {description}: {package} 可用")
        except ImportError:
            print(f"⚠️ {description}: {package} 不可用 (可选)")
    
    print("✅ 系统环境验证完成")
    return True

def test_castle_structure():
    """测试城堡结构定义"""
    print("\n🏰 测试城堡结构定义...")
    
    try:
        castle = CastleStructureDefinition()
        
        # 验证结构
        if castle.validate_structure():
            print(f"✅ 城堡结构验证通过")
            print(f"   总积木数: {castle.get_total_brick_count()}")
            print(f"   总层数: {castle.get_level_count()}")
            
            # 显示各层统计
            for level in range(1, castle.get_level_count() + 1):
                level_bricks = castle.get_level_bricks(level)
                print(f"   Level {level}: {len(level_bricks)} 个积木")
            
            return True
        else:
            print("❌ 城堡结构验证失败")
            return False
            
    except Exception as e:
        print(f"❌ 城堡结构测试失败: {e}")
        return False

def test_visualization():
    """测试可视化系统"""
    print("\n🎨 测试可视化系统...")
    
    try:
        visualizer = CastleVisualizer(real_time=False)
        
        # 添加测试积木
        visualizer.add_brick("test_brick", [0.5, 0, 0.065], 0, "brick_2x4", "tan")
        
        # 保存测试截图
        visualizer.save_screenshot('visualization_test.png')
        
        visualizer.close()
        
        print("✅ 可视化系统测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 可视化系统测试失败: {e}")
        return False

def run_simulation_mode(config: BuildConfig):
    """运行仿真模式"""
    print("\n🎮 启动仿真模式...")
    
    try:
        # 创建搭建器（仿真模式）
        builder = LegoCastleBuilder(config)
        
        print("开始城堡搭建仿真...")
        success = builder.build_castle()
        
        if success:
            print("🎉 仿真搭建成功完成！")
            
            # 显示最终状态
            status = builder.get_status()
            print(f"最终状态: {status}")
            
        else:
            print("❌ 仿真搭建失败")
        
        return success
        
    except Exception as e:
        print(f"❌ 仿真模式运行失败: {e}")
        return False
    
    finally:
        if 'builder' in locals():
            builder.cleanup()

def run_real_mode(config: BuildConfig):
    """运行真实机械臂模式"""
    print("\n🤖 启动真实机械臂模式...")
    
    # 安全确认
    response = input("⚠️ 即将启动真实机械臂，请确认安全环境。继续？(y/N): ")
    if response.lower() != 'y':
        print("操作已取消")
        return False
    
    try:
        # 创建搭建器（真实模式）
        builder = LegoCastleBuilder(config)
        
        print("开始真实城堡搭建...")
        success = builder.build_castle()
        
        if success:
            print("🎉 真实搭建成功完成！")
        else:
            print("❌ 真实搭建失败")
        
        return success
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        if 'builder' in locals():
            builder.emergency_stop()
        return False
        
    except Exception as e:
        print(f"❌ 真实模式运行失败: {e}")
        if 'builder' in locals():
            builder.emergency_stop()
        return False
    
    finally:
        if 'builder' in locals():
            builder.cleanup()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="LEGO城堡搭建系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python run_castle_builder.py --test              # 运行系统测试
  python run_castle_builder.py --simulation        # 运行仿真模式
  python run_castle_builder.py --real              # 运行真实机械臂模式
  python run_castle_builder.py --create-config     # 创建默认配置文件
        """
    )
    
    parser.add_argument('--config', '-c', default='castle_config.json',
                       help='配置文件路径 (默认: castle_config.json)')
    parser.add_argument('--log-level', default='INFO',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='日志级别 (默认: INFO)')
    parser.add_argument('--log-file', help='日志文件路径')
    
    # 运行模式
    mode_group = parser.add_mutually_exclusive_group(required=True)
    mode_group.add_argument('--test', action='store_true',
                           help='运行系统测试')
    mode_group.add_argument('--simulation', action='store_true',
                           help='运行仿真模式')
    mode_group.add_argument('--real', action='store_true',
                           help='运行真实机械臂模式')
    mode_group.add_argument('--create-config', action='store_true',
                           help='创建默认配置文件')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level, args.log_file)
    
    # 显示欢迎信息
    print("🏰 LEGO城堡搭建系统")
    print("基于参考图片的精确8层城堡搭建")
    print("=" * 50)
    
    # 创建配置文件
    if args.create_config:
        create_default_config(args.config)
        return 0
    
    # 验证系统环境
    if not validate_system():
        return 1
    
    # 运行测试
    if args.test:
        print("\n🧪 运行系统测试...")
        
        tests = [
            ("城堡结构定义", test_castle_structure),
            ("可视化系统", test_visualization)
        ]
        
        all_passed = True
        for test_name, test_func in tests:
            print(f"\n测试: {test_name}")
            if not test_func():
                all_passed = False
        
        if all_passed:
            print("\n✅ 所有测试通过！")
            return 0
        else:
            print("\n❌ 部分测试失败")
            return 1
    
    # 加载配置
    config = load_config(args.config)
    
    # 运行相应模式
    if args.simulation:
        success = run_simulation_mode(config)
    elif args.real:
        success = run_real_mode(config)
    else:
        print("❌ 未指定运行模式")
        return 1
    
    return 0 if success else 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️ 程序被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序异常退出: {e}")
        sys.exit(1)
