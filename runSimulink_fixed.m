function runSimulink_fixed(trajectories, T_total)
%% 修复版 Simulink 仿真函数
% 解决 MATLAB 轨迹数据传递给 Simulink 的格式问题
% 使用标准的 timeseries 格式确保兼容性

if nargin < 2
    T_total = 10;  % 默认仿真时间
end

modelName = 'YumiSimscape';
fprintf('\n=== 开始 Simulink 仿真 (修复版) ===\n');
fprintf('模型: %s\n', modelName);
fprintf('轨迹数量: %d\n', length(trajectories));
fprintf('仿真时间: %.1f 秒\n', T_total);

% 确保模型已加载
try
    if ~bdIsLoaded(modelName)
        fprintf('正在加载模型 %s...\n', modelName);
        open_system([modelName '.slx']);
        fprintf('✓ 模型加载成功\n');
    else
        fprintf('✓ 模型已加载\n');
    end
catch ME
    error('❌ 无法加载模型 %s: %s', modelName, ME.message);
end

% 设置仿真参数
try
    set_param(modelName, 'StopTime', num2str(T_total));
    set_param(modelName, 'SolverType', 'Variable-step');
    set_param(modelName, 'Solver', 'ode45');
    set_param(modelName, 'MaxStep', '0.01');
    fprintf('✓ 仿真参数设置完成\n');
catch ME
    warning('⚠️  仿真参数设置失败: %s', ME.message);
end

% 处理每个轨迹
success_count = 0;
for i = 1:length(trajectories)
    fprintf('\n--- 处理轨迹 %d/%d ---\n', i, length(trajectories));
    
    traj = trajectories{i};
    
    % 验证轨迹数据
    if ~isfield(traj, 'Q_smooth') || isempty(traj.Q_smooth)
        fprintf('❌ 轨迹 %d 数据无效，跳过\n', i);
        continue;
    end
    
    Q = traj.Q_smooth;
    N = size(Q, 1);
    
    fprintf('   手臂: %s\n', traj.arm);
    fprintf('   轨迹点数: %d\n', N);
    
    % 创建时间向量
    time_vector = linspace(0, T_total, N)';
    
    % 转换为 timeseries 格式
    try
        if strcmp(traj.arm, 'right')
            % 右臂运动，左臂静止
            trajDataRight = timeseries(Q, time_vector);
            trajDataLeft = timeseries(zeros(N, 7), time_vector);
            fprintf('   ✓ 右臂轨迹数据已准备\n');
        else
            % 左臂运动，右臂静止
            trajDataRight = timeseries(zeros(N, 7), time_vector);
            trajDataLeft = timeseries(Q, time_vector);
            fprintf('   ✓ 左臂轨迹数据已准备\n');
        end
        
        % 设置 timeseries 属性
        trajDataRight.Name = 'Right Arm Joint Angles';
        trajDataLeft.Name = 'Left Arm Joint Angles';
        
        % 传递到 base workspace
        assignin('base', 'trajDataRight', trajDataRight);
        assignin('base', 'trajDataLeft', trajDataLeft);
        assignin('base', 'T_total', T_total);
        assignin('base', 'Ts', time_vector(2) - time_vector(1));
        
        fprintf('   ✓ 数据已传递到工作空间\n');
        
    catch ME
        fprintf('   ❌ 数据格式转换失败: %s\n', ME.message);
        continue;
    end
    
    % 运行仿真
    fprintf('   🤖 开始仿真...');
    tic;
    
    try
        % 使用 sim 函数运行仿真
        simOut = sim(modelName, 'StopTime', num2str(T_total));
        
        elapsed = toc;
        fprintf(' 完成 (%.1f 秒)\n', elapsed);
        
        % 检查仿真结果
        if ~isempty(simOut)
            fprintf('   ✓ 仿真成功完成\n');
            success_count = success_count + 1;
            
            % 可选：保存仿真结果
            if i == 1
                fprintf('   💾 保存第一个轨迹的仿真结果...\n');
                save(sprintf('simulation_result_task_%d.mat', i), 'simOut');
            end
        else
            fprintf('   ⚠️  仿真结果为空\n');
        end
        
    catch ME
        elapsed = toc;
        fprintf(' 失败 (%.1f 秒)\n', elapsed);
        fprintf('   ❌ 仿真错误: %s\n', ME.message);
        
        % 尝试诊断问题
        diagnose_simulation_error(ME, modelName);
        continue;
    end
    
    % 短暂暂停以避免资源冲突
    pause(0.5);
end

% 总结结果
fprintf('\n=== 仿真完成总结 ===\n');
fprintf('成功完成: %d/%d 个轨迹\n', success_count, length(trajectories));

if success_count > 0
    fprintf('🎉 至少有一个轨迹仿真成功！\n');
    fprintf('💡 建议：检查 Simulink 模型的 3D 动画窗口查看机器人动作\n');
else
    fprintf('❌ 所有轨迹仿真都失败了\n');
    fprintf('💡 建议：检查模型配置和数据格式\n');
end

end

function diagnose_simulation_error(ME, modelName)
%% 诊断仿真错误的辅助函数

fprintf('   🔍 错误诊断:\n');

% 检查常见问题
if contains(ME.message, 'timeseries')
    fprintf('      - 可能是 timeseries 数据格式问题\n');
    fprintf('      - 建议检查 From Workspace 模块配置\n');
elseif contains(ME.message, 'dimension')
    fprintf('      - 可能是数据维度不匹配\n');
    fprintf('      - 建议检查关节数量和轨迹数据维度\n');
elseif contains(ME.message, 'solver')
    fprintf('      - 可能是求解器问题\n');
    fprintf('      - 建议调整求解器设置或步长\n');
elseif contains(ME.message, 'workspace')
    fprintf('      - 可能是工作空间变量问题\n');
    fprintf('      - 建议检查变量名称和类型\n');
else
    fprintf('      - 未知错误类型\n');
    fprintf('      - 完整错误信息: %s\n', ME.message);
end

% 检查模型状态
try
    model_status = get_param(modelName, 'SimulationStatus');
    fprintf('      - 模型状态: %s\n', model_status);
catch
    fprintf('      - 无法获取模型状态\n');
end

end
