#!/usr/bin/env python3
"""
碰撞检测模块
实现高精度的LEGO积木碰撞检测，包括AABB、OBB、SAT算法

作者: AI Assistant
日期: 2025-01-26
版本: 1.0
"""

import numpy as np
import logging
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import time

class CollisionType(Enum):
    """碰撞类型枚举"""
    NO_COLLISION = "no_collision"
    TOUCHING = "touching"
    PENETRATING = "penetrating"
    SEPARATING = "separating"

@dataclass
class AABB:
    """轴对齐包围盒"""
    min_point: np.ndarray
    max_point: np.ndarray
    
    def overlaps(self, other: 'AABB', tolerance: float = 0.0) -> bool:
        """检查与另一个AABB是否重叠"""
        return (self.min_point[0] <= other.max_point[0] + tolerance and
                self.max_point[0] >= other.min_point[0] - tolerance and
                self.min_point[1] <= other.max_point[1] + tolerance and
                self.max_point[1] >= other.min_point[1] - tolerance and
                self.min_point[2] <= other.max_point[2] + tolerance and
                self.max_point[2] >= other.min_point[2] - tolerance)
    
    def get_center(self) -> np.ndarray:
        """获取中心点"""
        return (self.min_point + self.max_point) / 2
    
    def get_extents(self) -> np.ndarray:
        """获取范围"""
        return (self.max_point - self.min_point) / 2

@dataclass
class OBB:
    """有向包围盒"""
    center: np.ndarray
    axes: np.ndarray      # 3x3旋转矩阵
    extents: np.ndarray   # 半长度
    
    def get_vertices(self) -> np.ndarray:
        """获取8个顶点"""
        vertices = []
        for i in range(8):
            vertex = self.center.copy()
            for j in range(3):
                sign = 1 if (i >> j) & 1 else -1
                vertex += sign * self.extents[j] * self.axes[:, j]
            vertices.append(vertex)
        return np.array(vertices)

@dataclass
class CollisionInfo:
    """碰撞信息"""
    object_a: str
    object_b: str
    collision_type: CollisionType
    contact_points: List[np.ndarray]
    contact_normal: np.ndarray
    penetration_depth: float
    separation_distance: float

class CollisionDetector:
    """碰撞检测器"""
    
    def __init__(self, tolerance: float = 1e-4):
        """初始化碰撞检测器"""
        self.tolerance = tolerance
        self.logger = logging.getLogger('CollisionDetector')
        
        # 性能统计
        self.detection_count = 0
        self.total_detection_time = 0.0
        
        # 缓存
        self.aabb_cache = {}
        self.obb_cache = {}
        
        self.logger.info("碰撞检测器初始化完成")
    
    def create_aabb_from_brick(self, position: np.ndarray, size: np.ndarray) -> AABB:
        """从积木创建AABB"""
        half_size = size / 2
        min_point = position - half_size
        max_point = position + half_size
        return AABB(min_point, max_point)
    
    def create_obb_from_brick(self, position: np.ndarray, orientation: float, 
                             size: np.ndarray) -> OBB:
        """从积木创建OBB"""
        # 创建旋转矩阵
        cos_theta = np.cos(orientation)
        sin_theta = np.sin(orientation)
        
        rotation_matrix = np.array([
            [cos_theta, -sin_theta, 0],
            [sin_theta, cos_theta, 0],
            [0, 0, 1]
        ])
        
        extents = size / 2
        return OBB(position, rotation_matrix, extents)
    
    def broad_phase_detection(self, objects: Dict) -> List[Tuple[str, str]]:
        """粗检测阶段：AABB重叠检测"""
        start_time = time.time()
        
        collision_pairs = []
        object_ids = list(objects.keys())
        
        # 更新AABB缓存
        for obj_id, obj_data in objects.items():
            position = np.array(obj_data['position'])
            size = np.array(obj_data.get('size', [0.0318, 0.0159, 0.0096]))
            self.aabb_cache[obj_id] = self.create_aabb_from_brick(position, size)
        
        # 检查所有对象对
        for i in range(len(object_ids)):
            for j in range(i + 1, len(object_ids)):
                obj_a = object_ids[i]
                obj_b = object_ids[j]
                
                aabb_a = self.aabb_cache[obj_a]
                aabb_b = self.aabb_cache[obj_b]
                
                if aabb_a.overlaps(aabb_b, self.tolerance):
                    collision_pairs.append((obj_a, obj_b))
        
        detection_time = time.time() - start_time
        self.total_detection_time += detection_time
        
        self.logger.debug(f"粗检测完成: {len(collision_pairs)}个潜在碰撞对, 耗时: {detection_time:.4f}s")
        return collision_pairs
    
    def narrow_phase_detection(self, collision_pairs: List[Tuple[str, str]], 
                              objects: Dict) -> List[CollisionInfo]:
        """精检测阶段：OBB碰撞检测"""
        start_time = time.time()
        
        collisions = []
        
        for obj_a, obj_b in collision_pairs:
            obj_data_a = objects[obj_a]
            obj_data_b = objects[obj_b]
            
            # 创建OBB
            pos_a = np.array(obj_data_a['position'])
            ori_a = obj_data_a.get('orientation', 0.0)
            size_a = np.array(obj_data_a.get('size', [0.0318, 0.0159, 0.0096]))
            
            pos_b = np.array(obj_data_b['position'])
            ori_b = obj_data_b.get('orientation', 0.0)
            size_b = np.array(obj_data_b.get('size', [0.0318, 0.0159, 0.0096]))
            
            obb_a = self.create_obb_from_brick(pos_a, ori_a, size_a)
            obb_b = self.create_obb_from_brick(pos_b, ori_b, size_b)
            
            # OBB碰撞检测
            collision_result = self.obb_collision_detection(obb_a, obb_b)
            
            if collision_result['collision']:
                collision_info = CollisionInfo(
                    object_a=obj_a,
                    object_b=obj_b,
                    collision_type=collision_result['type'],
                    contact_points=collision_result['contact_points'],
                    contact_normal=collision_result['normal'],
                    penetration_depth=collision_result['penetration'],
                    separation_distance=collision_result['separation']
                )
                collisions.append(collision_info)
        
        detection_time = time.time() - start_time
        self.total_detection_time += detection_time
        self.detection_count += 1
        
        self.logger.debug(f"精检测完成: {len(collisions)}个真实碰撞, 耗时: {detection_time:.4f}s")
        return collisions
    
    def obb_collision_detection(self, obb_a: OBB, obb_b: OBB) -> Dict:
        """OBB碰撞检测使用分离轴定理(SAT)"""
        # 计算相对位置
        relative_pos = obb_b.center - obb_a.center
        
        # 分离轴：两个OBB的3个轴 + 9个叉积轴
        axes = []
        
        # OBB A的3个轴
        for i in range(3):
            axes.append(obb_a.axes[:, i])
        
        # OBB B的3个轴
        for i in range(3):
            axes.append(obb_b.axes[:, i])
        
        # 叉积轴
        for i in range(3):
            for j in range(3):
                cross = np.cross(obb_a.axes[:, i], obb_b.axes[:, j])
                if np.linalg.norm(cross) > 1e-6:  # 避免平行轴
                    axes.append(cross / np.linalg.norm(cross))
        
        min_overlap = float('inf')
        separation_axis = None
        
        for axis in axes:
            # 投影OBB A
            proj_a = self._project_obb_onto_axis(obb_a, axis)
            
            # 投影OBB B
            proj_b = self._project_obb_onto_axis(obb_b, axis)
            
            # 检查分离
            overlap = min(proj_a[1], proj_b[1]) - max(proj_a[0], proj_b[0])
            
            if overlap <= self.tolerance:
                # 找到分离轴，无碰撞
                return {
                    'collision': False,
                    'type': CollisionType.NO_COLLISION,
                    'contact_points': [],
                    'normal': axis,
                    'penetration': 0.0,
                    'separation': -overlap
                }
            
            if overlap < min_overlap:
                min_overlap = overlap
                separation_axis = axis
        
        # 所有轴都有重叠，发生碰撞
        collision_type = CollisionType.PENETRATING if min_overlap > self.tolerance else CollisionType.TOUCHING
        
        # 计算接触点（简化实现）
        contact_points = [obb_a.center + (obb_b.center - obb_a.center) * 0.5]
        
        # 确保法向量指向正确方向
        if np.dot(separation_axis, obb_b.center - obb_a.center) < 0:
            separation_axis = -separation_axis
        
        return {
            'collision': True,
            'type': collision_type,
            'contact_points': contact_points,
            'normal': separation_axis,
            'penetration': min_overlap,
            'separation': 0.0
        }
    
    def _project_obb_onto_axis(self, obb: OBB, axis: np.ndarray) -> Tuple[float, float]:
        """将OBB投影到轴上"""
        # 投影中心点
        center_proj = np.dot(obb.center, axis)
        
        # 计算投影半径
        radius = 0.0
        for i in range(3):
            radius += obb.extents[i] * abs(np.dot(obb.axes[:, i], axis))
        
        return (center_proj - radius, center_proj + radius)
    
    def detect_lego_connections(self, objects: Dict) -> List[Dict]:
        """检测LEGO特定连接（螺柱-管道）"""
        connections = []
        
        # 简化的LEGO连接检测
        for obj_a_id, obj_a in objects.items():
            for obj_b_id, obj_b in objects.items():
                if obj_a_id >= obj_b_id:  # 避免重复检测
                    continue
                
                pos_a = np.array(obj_a['position'])
                pos_b = np.array(obj_b['position'])
                
                # 检查垂直距离
                vertical_distance = abs(pos_a[2] - pos_b[2])
                brick_height = 0.0096  # 9.6mm
                
                if vertical_distance < brick_height * 1.2:  # 允许20%误差
                    # 检查水平对齐
                    horizontal_distance = np.linalg.norm(pos_a[:2] - pos_b[:2])
                    
                    if horizontal_distance < 0.002:  # 2mm对齐容差
                        connection = {
                            'brick_a': obj_a_id,
                            'brick_b': obj_b_id,
                            'connection_type': 'stud_tube',
                            'position': (pos_a + pos_b) / 2,
                            'strength': 15.0  # N (卡扣力)
                        }
                        connections.append(connection)
        
        return connections
    
    def check_collisions(self, objects: Dict) -> List[CollisionInfo]:
        """执行完整的碰撞检测"""
        start_time = time.time()
        
        # 1. 粗检测
        collision_pairs = self.broad_phase_detection(objects)
        
        # 2. 精检测
        collisions = self.narrow_phase_detection(collision_pairs, objects)
        
        total_time = time.time() - start_time
        self.total_detection_time += total_time
        self.detection_count += 1
        
        self.logger.info(f"碰撞检测完成: {len(collisions)}个碰撞, 耗时: {total_time:.4f}s")
        
        return collisions
    
    def get_performance_stats(self) -> Dict:
        """获取性能统计"""
        if self.detection_count == 0:
            return {
                'total_detections': 0,
                'total_time': 0.0,
                'average_time': 0.0,
                'detections_per_second': 0.0
            }
        
        avg_time = self.total_detection_time / self.detection_count
        
        return {
            'total_detections': self.detection_count,
            'total_time': self.total_detection_time,
            'average_time': avg_time,
            'detections_per_second': 1.0 / avg_time if avg_time > 0 else 0.0
        }
    
    def reset_stats(self):
        """重置性能统计"""
        self.detection_count = 0
        self.total_detection_time = 0.0
        self.aabb_cache.clear()
        self.obb_cache.clear()

def main():
    """测试碰撞检测器"""
    print("🔍 碰撞检测器测试")
    print("=" * 30)
    
    # 创建检测器
    detector = CollisionDetector(tolerance=1e-4)
    
    # 创建测试对象
    objects = {
        'brick_1': {
            'position': [0.0, 0.0, 0.05],
            'orientation': 0.0,
            'size': [0.0318, 0.0159, 0.0096]
        },
        'brick_2': {
            'position': [0.02, 0.0, 0.05],
            'orientation': 0.0,
            'size': [0.0318, 0.0159, 0.0096]
        },
        'brick_3': {
            'position': [0.0, 0.0, 0.06],
            'orientation': 0.0,
            'size': [0.0318, 0.0159, 0.0096]
        }
    }
    
    try:
        # 执行碰撞检测
        print("执行碰撞检测...")
        collisions = detector.check_collisions(objects)
        
        print(f"\n检测结果:")
        print(f"  碰撞数量: {len(collisions)}")
        
        for i, collision in enumerate(collisions):
            print(f"  碰撞 {i+1}:")
            print(f"    对象: {collision.object_a} <-> {collision.object_b}")
            print(f"    类型: {collision.collision_type.value}")
            print(f"    穿透深度: {collision.penetration_depth:.6f}m")
            print(f"    接触法向量: {collision.contact_normal}")
        
        # 检测LEGO连接
        print("\n检测LEGO连接...")
        connections = detector.detect_lego_connections(objects)
        
        print(f"LEGO连接数量: {len(connections)}")
        for i, conn in enumerate(connections):
            print(f"  连接 {i+1}:")
            print(f"    积木: {conn['brick_a']} <-> {conn['brick_b']}")
            print(f"    类型: {conn['connection_type']}")
            print(f"    强度: {conn['strength']}N")
        
        # 性能统计
        stats = detector.get_performance_stats()
        print(f"\n性能统计:")
        print(f"  总检测次数: {stats['total_detections']}")
        print(f"  总耗时: {stats['total_time']:.4f}s")
        print(f"  平均耗时: {stats['average_time']:.4f}s")
        print(f"  检测频率: {stats['detections_per_second']:.1f} Hz")
        
        print("\n✅ 碰撞检测测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    main()
