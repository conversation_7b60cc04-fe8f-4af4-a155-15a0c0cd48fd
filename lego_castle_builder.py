#!/usr/bin/env python3
"""
LEGO城堡搭建系统 - 主控制器
基于参考图片实现精确的8层城堡搭建，使用YuMi双臂机械臂

作者: AI Assistant
日期: 2025-01-26
版本: 1.0
"""

import numpy as np
import time
import logging
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import json
import threading
from pathlib import Path

# 导入自定义模块
from castle_structure import CastleStructureDefinition
from yumi_controller import YuMiDualArmController
from physics_simulation import PhysicsSimulator
from collision_detection import CollisionDetector
from stability_analyzer import StabilityAnalyzer
from visualization import CastleVisualizer

class BuildStatus(Enum):
    """搭建状态枚举"""
    IDLE = "idle"
    PLANNING = "planning"
    BUILDING = "building"
    VERIFYING = "verifying"
    COMPLETED = "completed"
    ERROR = "error"
    PAUSED = "paused"

@dataclass
class BuildConfig:
    """搭建配置参数"""
    # 机械臂配置
    yumi_ip: str = "*************"
    left_arm_enabled: bool = True
    right_arm_enabled: bool = True
    
    # 物理参数
    brick_mass: float = 0.00253  # kg (2.53g)
    brick_density: float = 1040  # kg/m³
    friction_static: float = 0.6
    friction_kinetic: float = 0.4
    
    # 搭建参数
    placement_speed: float = 0.05  # m/s
    approach_height: float = 0.05  # m
    safety_margin: float = 0.002   # m
    
    # 验证参数
    stability_threshold: float = 0.7
    collision_tolerance: float = 1e-4  # m
    
    # 可视化配置
    enable_visualization: bool = True
    real_time_display: bool = True
    save_progress: bool = True

class LegoCastleBuilder:
    """LEGO城堡搭建主控制器"""
    
    def __init__(self, config: BuildConfig = None):
        """初始化城堡搭建系统"""
        self.config = config or BuildConfig()
        self.status = BuildStatus.IDLE
        self.current_level = 0
        self.completed_bricks = 0
        self.total_bricks = 0
        
        # 设置日志
        self._setup_logging()
        
        # 初始化核心组件
        self.logger.info("初始化LEGO城堡搭建系统...")
        self._initialize_components()
        
        # 加载城堡结构定义
        self.castle_structure = CastleStructureDefinition()
        self.total_bricks = self.castle_structure.get_total_brick_count()
        
        self.logger.info(f"系统初始化完成 - 总积木数: {self.total_bricks}")
    
    def _setup_logging(self):
        """设置日志系统"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('lego_castle_build.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('LegoCastleBuilder')
    
    def _initialize_components(self):
        """初始化所有核心组件"""
        try:
            # 初始化YuMi双臂控制器
            self.logger.info("初始化YuMi双臂控制器...")
            self.yumi_controller = YuMiDualArmController(
                ip_address=self.config.yumi_ip,
                left_enabled=self.config.left_arm_enabled,
                right_enabled=self.config.right_arm_enabled
            )
            
            # 初始化物理仿真器
            self.logger.info("初始化物理仿真器...")
            self.physics_sim = PhysicsSimulator(
                gravity=[0, 0, -9.81],
                time_step=0.01
            )
            
            # 初始化碰撞检测器
            self.logger.info("初始化碰撞检测器...")
            self.collision_detector = CollisionDetector(
                tolerance=self.config.collision_tolerance
            )
            
            # 初始化稳定性分析器
            self.logger.info("初始化稳定性分析器...")
            self.stability_analyzer = StabilityAnalyzer(
                threshold=self.config.stability_threshold
            )
            
            # 初始化可视化器
            if self.config.enable_visualization:
                self.logger.info("初始化可视化系统...")
                self.visualizer = CastleVisualizer(
                    real_time=self.config.real_time_display
                )
            else:
                self.visualizer = None
                
        except Exception as e:
            self.logger.error(f"组件初始化失败: {e}")
            raise
    
    def build_castle(self) -> bool:
        """执行完整的城堡搭建过程"""
        self.logger.info("开始LEGO城堡搭建...")
        self.status = BuildStatus.PLANNING
        
        try:
            # 1. 系统预检查
            if not self._pre_build_check():
                return False
            
            # 2. 生成搭建计划
            build_plan = self._generate_build_plan()
            self.logger.info(f"生成搭建计划 - 共{len(build_plan)}个步骤")
            
            # 3. 执行搭建
            self.status = BuildStatus.BUILDING
            success = self._execute_build_plan(build_plan)
            
            if success:
                self.status = BuildStatus.COMPLETED
                self.logger.info("🏰 LEGO城堡搭建成功完成！")
                self._post_build_verification()
            else:
                self.status = BuildStatus.ERROR
                self.logger.error("❌ 城堡搭建失败")
            
            return success
            
        except Exception as e:
            self.logger.error(f"搭建过程中发生错误: {e}")
            self.status = BuildStatus.ERROR
            return False
    
    def _pre_build_check(self) -> bool:
        """搭建前系统检查"""
        self.logger.info("执行搭建前系统检查...")
        
        checks = [
            ("YuMi连接", self._check_yumi_connection),
            ("工作空间", self._check_workspace),
            ("积木供应", self._check_brick_supply),
            ("安全系统", self._check_safety_systems)
        ]
        
        for check_name, check_func in checks:
            self.logger.info(f"检查{check_name}...")
            if not check_func():
                self.logger.error(f"❌ {check_name}检查失败")
                return False
            self.logger.info(f"✅ {check_name}检查通过")
        
        return True
    
    def _check_yumi_connection(self) -> bool:
        """检查YuMi连接状态"""
        return self.yumi_controller.is_connected()
    
    def _check_workspace(self) -> bool:
        """检查工作空间状态"""
        # 检查工作台是否清空，积木是否就位等
        return True  # 简化实现
    
    def _check_brick_supply(self) -> bool:
        """检查积木供应"""
        # 检查是否有足够的积木
        return True  # 简化实现
    
    def _check_safety_systems(self) -> bool:
        """检查安全系统"""
        # 检查急停、防护等安全系统
        return True  # 简化实现
    
    def _generate_build_plan(self) -> List[Dict]:
        """生成详细的搭建计划"""
        self.logger.info("生成搭建计划...")
        
        build_plan = []
        
        # 按层级生成搭建步骤
        for level in range(1, 9):  # Level 1-8
            level_bricks = self.castle_structure.get_level_bricks(level)
            
            for brick_info in level_bricks:
                # 分配给左臂或右臂
                arm = self._assign_arm(brick_info['position'])
                
                step = {
                    'step_id': len(build_plan) + 1,
                    'level': level,
                    'brick_id': brick_info['id'],
                    'brick_type': brick_info['type'],
                    'target_position': brick_info['position'],
                    'target_orientation': brick_info['orientation'],
                    'assigned_arm': arm,
                    'dependencies': brick_info.get('dependencies', []),
                    'verification_required': True
                }
                
                build_plan.append(step)
        
        # 优化搭建顺序
        optimized_plan = self._optimize_build_sequence(build_plan)
        
        return optimized_plan
    
    def _assign_arm(self, position: List[float]) -> str:
        """根据位置分配机械臂"""
        x, y, z = position
        
        # 简单的分配策略：根据Y坐标分配
        if y < 0:
            return 'left'
        else:
            return 'right'
    
    def _optimize_build_sequence(self, build_plan: List[Dict]) -> List[Dict]:
        """优化搭建序列"""
        # 按层级和依赖关系排序
        def sort_key(step):
            return (step['level'], step['step_id'])
        
        return sorted(build_plan, key=sort_key)
    
    def _execute_build_plan(self, build_plan: List[Dict]) -> bool:
        """执行搭建计划"""
        self.logger.info(f"开始执行搭建计划 - {len(build_plan)}个步骤")
        
        for i, step in enumerate(build_plan):
            self.logger.info(f"执行步骤 {i+1}/{len(build_plan)}: Level {step['level']}, Brick {step['brick_id']}")
            
            # 更新进度
            self.current_level = step['level']
            
            # 执行单个搭建步骤
            if not self._execute_build_step(step):
                self.logger.error(f"步骤 {i+1} 执行失败")
                return False
            
            self.completed_bricks += 1
            
            # 更新可视化
            if self.visualizer:
                self.visualizer.update_progress(
                    level=self.current_level,
                    completed=self.completed_bricks,
                    total=self.total_bricks
                )
            
            # 进度报告
            progress = (self.completed_bricks / self.total_bricks) * 100
            self.logger.info(f"进度: {progress:.1f}% ({self.completed_bricks}/{self.total_bricks})")
        
        return True
    
    def _execute_build_step(self, step: Dict) -> bool:
        """执行单个搭建步骤"""
        try:
            # 1. 检查依赖
            if not self._check_dependencies(step):
                return False
            
            # 2. 拾取积木
            if not self._pick_brick(step):
                return False
            
            # 3. 移动到目标位置
            if not self._place_brick(step):
                return False
            
            # 4. 验证放置
            if step['verification_required']:
                if not self._verify_placement(step):
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"执行搭建步骤失败: {e}")
            return False
    
    def _check_dependencies(self, step: Dict) -> bool:
        """检查步骤依赖"""
        # 检查依赖的积木是否已经放置
        return True  # 简化实现
    
    def _pick_brick(self, step: Dict) -> bool:
        """拾取积木"""
        arm = step['assigned_arm']
        brick_type = step['brick_type']
        
        # 获取积木供应位置
        supply_position = self._get_brick_supply_position(brick_type)
        
        # 执行拾取动作
        success = self.yumi_controller.pick_brick(
            arm=arm,
            position=supply_position,
            brick_type=brick_type
        )
        
        return success
    
    def _place_brick(self, step: Dict) -> bool:
        """放置积木"""
        arm = step['assigned_arm']
        target_pos = step['target_position']
        target_ori = step['target_orientation']
        
        # 执行放置动作
        success = self.yumi_controller.place_brick(
            arm=arm,
            position=target_pos,
            orientation=target_ori,
            approach_height=self.config.approach_height,
            speed=self.config.placement_speed
        )
        
        return success
    
    def _verify_placement(self, step: Dict) -> bool:
        """验证积木放置"""
        # 1. 位置验证
        actual_pos = self._get_actual_brick_position(step['brick_id'])
        target_pos = step['target_position']
        
        position_error = np.linalg.norm(np.array(actual_pos) - np.array(target_pos))
        if position_error > self.config.safety_margin:
            self.logger.warning(f"位置误差过大: {position_error:.4f}m")
            return False
        
        # 2. 稳定性验证
        stability_score = self.stability_analyzer.analyze_current_structure()
        if stability_score < self.config.stability_threshold:
            self.logger.warning(f"稳定性不足: {stability_score:.3f}")
            return False
        
        # 3. 碰撞检测
        if self.collision_detector.check_collisions():
            self.logger.warning("检测到碰撞")
            return False
        
        return True
    
    def _get_brick_supply_position(self, brick_type: str) -> List[float]:
        """获取积木供应位置"""
        # 根据积木类型返回供应位置
        supply_positions = {
            'brick_2x4': [0.3, -0.2, 0.05],
            'brick_2x2': [0.3, -0.15, 0.05],
            'cone': [0.3, -0.1, 0.05]
        }
        return supply_positions.get(brick_type, [0.3, -0.2, 0.05])
    
    def _get_actual_brick_position(self, brick_id: str) -> List[float]:
        """获取积木实际位置"""
        # 通过视觉系统或传感器获取实际位置
        return [0, 0, 0]  # 简化实现
    
    def _post_build_verification(self):
        """搭建后验证"""
        self.logger.info("执行搭建后验证...")
        
        # 1. 整体结构验证
        structure_valid = self._verify_complete_structure()
        
        # 2. 稳定性分析
        final_stability = self.stability_analyzer.analyze_complete_castle()
        
        # 3. 生成报告
        self._generate_build_report(structure_valid, final_stability)
    
    def _verify_complete_structure(self) -> bool:
        """验证完整结构"""
        # 与参考图片对比验证
        return True  # 简化实现
    
    def _generate_build_report(self, structure_valid: bool, stability_score: float):
        """生成搭建报告"""
        report = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'total_bricks': self.total_bricks,
            'completed_bricks': self.completed_bricks,
            'structure_valid': structure_valid,
            'final_stability': stability_score,
            'build_time': time.time(),  # 需要记录开始时间
            'status': self.status.value
        }
        
        # 保存报告
        with open('build_report.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        self.logger.info(f"搭建报告已生成: {report}")
    
    def get_status(self) -> Dict:
        """获取当前状态"""
        return {
            'status': self.status.value,
            'current_level': self.current_level,
            'completed_bricks': self.completed_bricks,
            'total_bricks': self.total_bricks,
            'progress': (self.completed_bricks / self.total_bricks) * 100 if self.total_bricks > 0 else 0
        }
    
    def pause_build(self):
        """暂停搭建"""
        self.status = BuildStatus.PAUSED
        self.logger.info("搭建已暂停")
    
    def resume_build(self):
        """恢复搭建"""
        if self.status == BuildStatus.PAUSED:
            self.status = BuildStatus.BUILDING
            self.logger.info("搭建已恢复")
    
    def emergency_stop(self):
        """紧急停止"""
        self.status = BuildStatus.ERROR
        self.yumi_controller.emergency_stop()
        self.logger.warning("⚠️ 紧急停止已激活")
    
    def cleanup(self):
        """清理资源"""
        self.logger.info("清理系统资源...")
        
        if hasattr(self, 'yumi_controller'):
            self.yumi_controller.disconnect()
        
        if hasattr(self, 'physics_sim'):
            self.physics_sim.cleanup()
        
        if hasattr(self, 'visualizer') and self.visualizer:
            self.visualizer.close()
        
        self.logger.info("系统资源清理完成")

def main():
    """主函数"""
    print("🏰 LEGO城堡搭建系统启动")
    print("基于参考图片的精确8层城堡搭建")
    print("=" * 50)
    
    # 创建配置
    config = BuildConfig(
        yumi_ip="*************",
        enable_visualization=True,
        real_time_display=True
    )
    
    # 创建搭建器
    builder = LegoCastleBuilder(config)
    
    try:
        # 执行搭建
        success = builder.build_castle()
        
        if success:
            print("🎉 城堡搭建成功完成！")
        else:
            print("❌ 城堡搭建失败")
    
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断搭建")
        builder.emergency_stop()
    
    except Exception as e:
        print(f"❌ 系统错误: {e}")
        builder.emergency_stop()
    
    finally:
        builder.cleanup()
        print("系统已安全关闭")

if __name__ == "__main__":
    main()
