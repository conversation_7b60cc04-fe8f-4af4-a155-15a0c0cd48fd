function [yumi, qHome, table, ax] = setupRobotEnv_fixed()
    % Fixed version of robot environment setup
    % Addresses compatibility issues with current MATLAB version
    
    fprintf('Starting YuMi Robot Environment Setup (Fixed Version)...\n');
    
    try
        % Load YuMi robot model
        fprintf('Loading YuMi robot model...\n');
        yumi = loadrobot('abbYumi','DataFormat','row','Gravity',[0 0 -9.81]);
        qHome = yumi.homeConfiguration;
        
        % Check robot properties (compatibility fix)
        fprintf('✓ YuMi robot loaded successfully\n');
        fprintf('  - Number of bodies: %d\n', yumi.NumBodies);
        fprintf('  - Degrees of freedom: %d\n', length(qHome));
        fprintf('  - Base frame: %s\n', yumi.BaseName);
        
        % Test kinematics
        try
            T_left = getTransform(yumi, qHome, 'gripper_l_base');
            T_right = getTransform(yumi, qHome, 'gripper_r_base');
            fprintf('  - Left arm end-effector: [%.3f, %.3f, %.3f]\n', T_left(1,4), T_left(2,4), T_left(3,4));
            fprintf('  - Right arm end-effector: [%.3f, %.3f, %.3f]\n', T_right(1,4), T_right(2,4), T_right(3,4));
            fprintf('✓ Kinematics test passed\n');
        catch ME
            fprintf('⚠ Kinematics test failed: %s\n', ME.message);
        end
        
        % Test inverse kinematics
        try
            ik_left = inverseKinematics('RigidBodyTree', yumi, 'SolverAlgorithm', 'BFGSGradientProjection');
            ik_left.SolverParameters.AllowRandomRestart = false;
            fprintf('✓ Inverse kinematics solver created\n');
        catch ME
            fprintf('⚠ Inverse kinematics setup failed: %s\n', ME.message);
        end
        
    catch ME
        fprintf('✗ YuMi robot loading failed: %s\n', ME.message);
        yumi = [];
        qHome = [];
    end
    
    % Create visualization environment
    fprintf('Creating visualization environment...\n');
    figure('Name', 'YuMi LEGO Environment', 'Position', [100, 100, 1200, 800]);
    ax = gca;
    hold on;
    
    % Create table using simple patch (avoid collisionBox dependency issues)
    tableLength = 0.72; 
    tableWidth  = 0.6; 
    tableHeight = 0.02;
    tableCenter = [0.5, 0, 0.05];
    
    % Table surface
    table_x = tableCenter(1) + [-tableLength/2, tableLength/2, tableLength/2, -tableLength/2];
    table_y = tableCenter(2) + [-tableWidth/2, -tableWidth/2, tableWidth/2, tableWidth/2];
    table_z = tableCenter(3) * ones(1, 4);
    
    table = patch(table_x, table_y, table_z, [0.8, 0.8, 0.8], 'FaceAlpha', 0.7, 'EdgeColor', 'k');
    
    % Visual parameters
    tableZ = 0.06;  % Table surface height
    alpha  = 0.3;   % Block transparency
    
    % Central stacking area (castle)
    castle_x = [0.35, 0.65];
    castle_y = [-0.1, 0.1];
    castle_corners = [
        castle_x(1), castle_y(1), tableZ;
        castle_x(2), castle_y(1), tableZ;
        castle_x(2), castle_y(2), tableZ;
        castle_x(1), castle_y(2), tableZ
    ];
    patch(castle_corners(:,1), castle_corners(:,2), castle_corners(:,3), [0, 1, 0], 'FaceAlpha', alpha);
    text(0.5, 0, tableZ + 0.02, 'Castle Area', 'HorizontalAlignment', 'center', 'FontSize', 10, 'FontWeight', 'bold');
    
    % Right hand LEGO area
    right_x = [0.7, 0.9];
    right_y = [-0.2, 0.2];
    right_corners = [
        right_x(1), right_y(1), tableZ;
        right_x(2), right_y(1), tableZ;
        right_x(2), right_y(2), tableZ;
        right_x(1), right_y(2), tableZ
    ];
    patch(right_corners(:,1), right_corners(:,2), right_corners(:,3), [0, 0, 1], 'FaceAlpha', alpha);
    text(0.8, 0, tableZ + 0.02, 'Right Arm\nLEGO Area', 'HorizontalAlignment', 'center', 'FontSize', 9);
    
    % Left hand LEGO area
    left_x = [0.1, 0.3];
    left_y = [-0.2, 0.2];
    left_corners = [
        left_x(1), left_y(1), tableZ;
        left_x(2), left_y(1), tableZ;
        left_x(2), left_y(2), tableZ;
        left_x(1), left_y(2), tableZ
    ];
    patch(left_corners(:,1), left_corners(:,2), left_corners(:,3), [1, 0, 0], 'FaceAlpha', alpha);
    text(0.2, 0, tableZ + 0.02, 'Left Arm\nLEGO Area', 'HorizontalAlignment', 'center', 'FontSize', 9);
    
    % View settings
    xlabel('X (m)'); ylabel('Y (m)'); zlabel('Z (m)');
    title('YuMi LEGO Working Environment', 'FontSize', 14, 'FontWeight', 'bold');
    axis equal; grid on;
    view(45, 30);
    
    % Set axis limits for better view
    xlim([-0.1, 1.1]);
    ylim([-0.4, 0.4]);
    zlim([0, 0.8]);
    
    % Display robot (if successfully loaded)
    if ~isempty(yumi)
        try
            show(yumi, qHome, 'Parent', ax, 'Visuals', 'on', 'Collision', 'off');
            fprintf('✓ Robot visualization displayed\n');
        catch ME
            fprintf('⚠ Robot visualization failed: %s\n', ME.message);
            % Try alternative visualization
            try
                show(yumi, qHome);
                fprintf('✓ Robot displayed with default settings\n');
            catch ME2
                fprintf('✗ Robot visualization completely failed: %s\n', ME2.message);
            end
        end
    end
    
    % Add coordinate frame indicators
    quiver3(0, 0, 0, 0.1, 0, 0, 'r', 'LineWidth', 2, 'MaxHeadSize', 0.5); % X-axis
    quiver3(0, 0, 0, 0, 0.1, 0, 'g', 'LineWidth', 2, 'MaxHeadSize', 0.5); % Y-axis
    quiver3(0, 0, 0, 0, 0, 0.1, 'b', 'LineWidth', 2, 'MaxHeadSize', 0.5); % Z-axis
    text(0.05, 0, 0, 'X', 'Color', 'r', 'FontSize', 12, 'FontWeight', 'bold');
    text(0, 0.05, 0, 'Y', 'Color', 'g', 'FontSize', 12, 'FontWeight', 'bold');
    text(0, 0, 0.05, 'Z', 'Color', 'b', 'FontSize', 12, 'FontWeight', 'bold');
    
    fprintf('✓ Environment setup completed\n');
    
    % Test basic robot movements
    if ~isempty(yumi) && ~isempty(qHome)
        fprintf('Testing basic robot movements...\n');
        try
            % Test small joint movements
            q_test = qHome;
            q_test(1) = q_test(1) + 0.1; % Small movement of first joint
            
            % Check if configuration is valid
            if all(q_test >= yumi.PositionLimits(:,1)') && all(q_test <= yumi.PositionLimits(:,2)')
                T_test = getTransform(yumi, q_test, 'gripper_l_base');
                fprintf('✓ Basic movement test passed\n');
            else
                fprintf('⚠ Joint limits exceeded in test\n');
            end
        catch ME
            fprintf('⚠ Movement test failed: %s\n', ME.message);
        end
    end
    
    % Summary
    fprintf('\n=== Environment Setup Summary ===\n');
    if ~isempty(yumi)
        fprintf('✓ YuMi robot: Successfully loaded\n');
        fprintf('✓ Kinematics: Working\n');
        fprintf('✓ Visualization: Active\n');
        fprintf('✓ Work areas: Defined\n');
        fprintf('Status: READY for trajectory planning\n');
    else
        fprintf('✗ YuMi robot: Failed to load\n');
        fprintf('Status: NEEDS TROUBLESHOOTING\n');
    end
    fprintf('================================\n');
end
