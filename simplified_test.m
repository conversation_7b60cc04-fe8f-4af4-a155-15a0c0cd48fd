%% Simplified YuMi LEGO Test (MATLAB Only)
% This script tests the core functionality without Simulink dependencies

clc; clear; close all;
fprintf('=== Simplified YuMi LEGO Test ===\n\n');

%% 1. Load robot model
fprintf('1. Loading YuMi robot model...\n');
try
    yumi = loadrobot('abbYumi', 'DataFormat', 'row');
    qHome = yumi.homeConfiguration;
    fprintf('   ✓ YuMi model loaded successfully\n');
    fprintf('   ✓ Joint count: %d\n', length(qHome));
    fprintf('   ✓ Home configuration: [%.3f, %.3f, %.3f, ...]\n', qHome(1:3));
catch ME
    fprintf('   ❌ YuMi model loading failed: %s\n', ME.message);
    return;
end

%% 2. Load LEGO configuration
fprintf('\n2. Loading LEGO configuration...\n');
try
    brick_config = lego_config();
    fprintf('   ✓ LEGO config loaded successfully\n');
    fprintf('   ✓ Target positions: %d\n', size(brick_config.all_targets, 1));
    fprintf('   ✓ Task sequence: %d tasks\n', length(brick_config.task_sequence));
    
    % Display first few targets
    fprintf('   First 3 target positions:\n');
    for i = 1:min(3, size(brick_config.all_targets, 1))
        pos = brick_config.all_targets(i, 1:3);
        fprintf('     Target %d: [%.4f, %.4f, %.4f]\n', i, pos);
    end
    
catch ME
    fprintf('   ❌ LEGO config loading failed: %s\n', ME.message);
    return;
end

%% 3. Test coordinate verification
fprintf('\n3. Testing coordinate verification...\n');
try
    % Run coordinate verification
    targets = brick_config.all_targets;
    x_range = [min(targets(:,1)), max(targets(:,1))];
    y_range = [min(targets(:,2)), max(targets(:,2))];
    z_range = [min(targets(:,3)), max(targets(:,3))];
    
    fprintf('   ✓ Coordinate ranges calculated\n');
    fprintf('     X range: [%.4f, %.4f] m\n', x_range);
    fprintf('     Y range: [%.4f, %.4f] m\n', y_range);
    fprintf('     Z range: [%.4f, %.4f] m\n', z_range);
    
    % Check workspace bounds
    workspace_x = [0.35, 0.65];
    workspace_y = [-0.1, 0.1];
    
    if x_range(1) >= workspace_x(1) && x_range(2) <= workspace_x(2) && ...
       y_range(1) >= workspace_y(1) && y_range(2) <= workspace_y(2)
        fprintf('   ✓ All targets within workspace bounds\n');
    else
        fprintf('   ⚠️  Some targets outside workspace bounds\n');
    end
    
catch ME
    fprintf('   ❌ Coordinate verification failed: %s\n', ME.message);
end

%% 4. Test trajectory planning (simplified)
fprintf('\n4. Testing trajectory planning...\n');
try
    % Create a minimal test configuration
    test_config = brick_config;
    test_config.task_sequence = brick_config.task_sequence(1);  % Only first task
    
    fprintf('   Testing with task: %s arm -> Target %d\n', ...
        test_config.task_sequence(1).arm, test_config.task_sequence(1).target_id);
    
    % Attempt trajectory planning
    trajectories = planTrajectory(yumi, test_config, qHome);
    
    if ~isempty(trajectories)
        fprintf('   ✓ Trajectory planning successful!\n');
        fprintf('   ✓ Generated %d trajectory\n', length(trajectories));
        
        % Analyze trajectory
        traj = trajectories{1};
        if isfield(traj, 'Q_smooth') && ~isempty(traj.Q_smooth)
            fprintf('   ✓ Trajectory has %d waypoints\n', size(traj.Q_smooth, 1));
            fprintf('   ✓ Trajectory for %s arm\n', traj.arm);
            
            % Check trajectory validity
            q_range = [min(traj.Q_smooth(:)); max(traj.Q_smooth(:))];
            fprintf('   ✓ Joint angle range: [%.3f, %.3f] rad\n', q_range);
            
        else
            fprintf('   ⚠️  Trajectory data incomplete\n');
        end
    else
        fprintf('   ❌ Trajectory planning failed\n');
    end
    
catch ME
    fprintf('   ❌ Trajectory planning error: %s\n', ME.message);
    fprintf('   Error location: %s, line %d\n', ME.stack(1).file, ME.stack(1).line);
end

%% 5. Test inverse kinematics (basic check)
fprintf('\n5. Testing inverse kinematics...\n');
try
    % Test IK for a simple target position
    target_pos = brick_config.all_targets(1, 1:3);  % First target
    target_orientation = [0, 0, 0];  % Simple orientation
    
    % Create transformation matrix
    T_target = [eye(3), target_pos'; 0, 0, 0, 1];
    
    % Test IK
    ik = inverseKinematics('RigidBodyTree', yumi);
    weights = [0.1, 0.1, 0.1, 1, 1, 1];
    
    % Try right arm end effector
    try
        [q_solution, info] = ik('gripper_r_base', T_target, weights, qHome);
        if info.ExitFlag > 0
            fprintf('   ✓ Right arm IK successful\n');
            fprintf('   ✓ Solution found with exit flag: %d\n', info.ExitFlag);
        else
            fprintf('   ⚠️  Right arm IK failed, exit flag: %d\n', info.ExitFlag);
        end
    catch
        fprintf('   ⚠️  Right arm IK test failed\n');
    end
    
    % Try left arm end effector
    try
        [q_solution, info] = ik('gripper_l_base', T_target, weights, qHome);
        if info.ExitFlag > 0
            fprintf('   ✓ Left arm IK successful\n');
        else
            fprintf('   ⚠️  Left arm IK failed, exit flag: %d\n', info.ExitFlag);
        end
    catch
        fprintf('   ⚠️  Left arm IK test failed\n');
    end
    
catch ME
    fprintf('   ❌ IK testing failed: %s\n', ME.message);
end

%% 6. Summary and recommendations
fprintf('\n=== Test Summary ===\n');
fprintf('✓ YuMi robot model: Loaded successfully\n');
fprintf('✓ LEGO configuration: Loaded successfully\n');
fprintf('✓ Coordinate system: Verified\n');

if exist('trajectories', 'var') && ~isempty(trajectories)
    fprintf('✓ Trajectory planning: Working\n');
    trajectory_ok = true;
else
    fprintf('❌ Trajectory planning: Failed\n');
    trajectory_ok = false;
end

fprintf('\n=== Next Steps Recommendations ===\n');
if trajectory_ok
    fprintf('🎉 Core functionality is working!\n');
    fprintf('💡 Recommendations:\n');
    fprintf('   1. Try running more trajectory tests\n');
    fprintf('   2. Implement MATLAB-based visualization\n');
    fprintf('   3. Work on Simulink compatibility separately\n');
    fprintf('   4. Focus on trajectory optimization\n');
else
    fprintf('⚠️  Trajectory planning needs attention\n');
    fprintf('💡 Recommendations:\n');
    fprintf('   1. Debug planTrajectory.m function\n');
    fprintf('   2. Check inverse kinematics settings\n');
    fprintf('   3. Verify target positions are reachable\n');
    fprintf('   4. Simplify initial test cases\n');
end

fprintf('\n🔧 To continue development:\n');
fprintf('   - Run: matlab_visualization_test.m (if created)\n');
fprintf('   - Debug: planTrajectory.m for specific errors\n');
fprintf('   - Alternative: Focus on MATLAB animation first\n');

fprintf('\nSimplified test complete!\n');
