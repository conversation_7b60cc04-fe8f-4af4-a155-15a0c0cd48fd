#!/usr/bin/env python3
"""
物理仿真模块
基于PyBullet实现LEGO积木的精确物理仿真，包括重力、碰撞、摩擦等

作者: AI Assistant
日期: 2025-01-26
版本: 1.0
"""

import numpy as np
import time
import logging
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
import pybullet as p
import pybullet_data
from pathlib import Path

@dataclass
class PhysicsProperties:
    """物理属性数据类"""
    # 几何属性
    length: float = 0.0318      # 31.8mm
    width: float = 0.0159       # 15.9mm  
    height: float = 0.0096      # 9.6mm
    
    # 材料属性
    density: float = 1040       # kg/m³ (ABS塑料)
    elastic_modulus: float = 2.3e9  # Pa
    poisson_ratio: float = 0.35
    
    # 摩擦属性
    friction_static: float = 0.6
    friction_kinetic: float = 0.4
    restitution: float = 0.3
    
    # 接触属性
    contact_stiffness: float = 1e6  # N/m
    contact_damping: float = 100    # Ns/m
    
    def get_mass(self) -> float:
        """计算质量"""
        volume = self.length * self.width * self.height
        return volume * self.density
    
    def get_inertia(self) -> List[float]:
        """计算惯性张量"""
        mass = self.get_mass()
        Ixx = mass * (self.width**2 + self.height**2) / 12
        Iyy = mass * (self.length**2 + self.height**2) / 12
        Izz = mass * (self.length**2 + self.width**2) / 12
        return [Ixx, Iyy, Izz]

class PhysicsSimulator:
    """物理仿真器"""
    
    def __init__(self, gravity: List[float] = [0, 0, -9.81], 
                 time_step: float = 1/240, gui: bool = False):
        """初始化物理仿真器"""
        self.gravity = gravity
        self.time_step = time_step
        self.gui = gui
        
        # 设置日志
        self.logger = logging.getLogger('PhysicsSimulator')
        
        # 仿真状态
        self.simulation_running = False
        self.physics_client = None
        self.objects = {}  # 存储仿真对象
        self.step_count = 0
        
        # 物理属性
        self.physics_props = PhysicsProperties()
        
        # 初始化PyBullet
        self._initialize_pybullet()
        
        self.logger.info("物理仿真器初始化完成")
    
    def _initialize_pybullet(self):
        """初始化PyBullet物理引擎"""
        try:
            # 连接物理引擎
            if self.gui:
                self.physics_client = p.connect(p.GUI)
                p.configureDebugVisualizer(p.COV_ENABLE_GUI, 1)
            else:
                self.physics_client = p.connect(p.DIRECT)
            
            # 设置搜索路径
            p.setAdditionalSearchPath(pybullet_data.getDataPath())
            
            # 设置重力
            p.setGravity(*self.gravity)
            
            # 设置时间步长
            p.setTimeStep(self.time_step)
            
            # 设置实时仿真
            p.setRealTimeSimulation(0)  # 手动步进
            
            # 加载地面
            self.ground_id = p.loadURDF("plane.urdf")
            
            # 设置地面材质
            p.changeDynamics(
                self.ground_id, -1,
                lateralFriction=self.physics_props.friction_static,
                restitution=self.physics_props.restitution
            )
            
            self.logger.info("✅ PyBullet初始化成功")
            
        except Exception as e:
            self.logger.error(f"❌ PyBullet初始化失败: {e}")
            raise
    
    def create_brick(self, brick_id: str, position: List[float], 
                    orientation: float = 0, brick_type: str = "brick_2x4") -> int:
        """创建LEGO积木"""
        try:
            # 根据积木类型设置尺寸
            if brick_type == "brick_2x4":
                size = [self.physics_props.length/2, self.physics_props.width/2, self.physics_props.height/2]
            elif brick_type == "brick_2x2":
                size = [self.physics_props.width/2, self.physics_props.width/2, self.physics_props.height/2]
            elif brick_type == "cone":
                # 圆锥形状，使用圆柱体近似
                size = [self.physics_props.width/4, self.physics_props.width/4, self.physics_props.height]
            else:
                size = [self.physics_props.length/2, self.physics_props.width/2, self.physics_props.height/2]
            
            # 创建碰撞形状
            if brick_type == "cone":
                collision_shape = p.createCollisionShape(
                    p.GEOM_CYLINDER,
                    radius=size[0],
                    height=size[2]
                )
                visual_shape = p.createVisualShape(
                    p.GEOM_CYLINDER,
                    radius=size[0],
                    length=size[2],
                    rgbaColor=[0.8, 0.6, 0.4, 1.0]
                )
            else:
                collision_shape = p.createCollisionShape(
                    p.GEOM_BOX,
                    halfExtents=size
                )
                visual_shape = p.createVisualShape(
                    p.GEOM_BOX,
                    halfExtents=size,
                    rgbaColor=[0.8, 0.6, 0.4, 1.0]
                )
            
            # 计算四元数朝向
            quaternion = p.getQuaternionFromEuler([0, 0, np.radians(orientation)])
            
            # 创建多体对象
            body_id = p.createMultiBody(
                baseMass=self.physics_props.get_mass(),
                baseCollisionShapeIndex=collision_shape,
                baseVisualShapeIndex=visual_shape,
                basePosition=position,
                baseOrientation=quaternion,
                baseInertialFramePosition=[0, 0, 0],
                baseInertialFrameOrientation=[0, 0, 0, 1]
            )
            
            # 设置物理属性
            p.changeDynamics(
                body_id, -1,
                mass=self.physics_props.get_mass(),
                lateralFriction=self.physics_props.friction_static,
                rollingFriction=self.physics_props.friction_kinetic * 0.1,
                restitution=self.physics_props.restitution,
                contactStiffness=self.physics_props.contact_stiffness,
                contactDamping=self.physics_props.contact_damping
            )
            
            # 存储对象信息
            self.objects[brick_id] = {
                'body_id': body_id,
                'type': brick_type,
                'initial_position': position.copy(),
                'initial_orientation': orientation
            }
            
            self.logger.debug(f"创建积木: {brick_id} at {position}")
            return body_id
            
        except Exception as e:
            self.logger.error(f"创建积木失败: {e}")
            return -1
    
    def remove_brick(self, brick_id: str):
        """移除积木"""
        if brick_id in self.objects:
            body_id = self.objects[brick_id]['body_id']
            p.removeBody(body_id)
            del self.objects[brick_id]
            self.logger.debug(f"移除积木: {brick_id}")
    
    def get_brick_pose(self, brick_id: str) -> Tuple[List[float], List[float]]:
        """获取积木位姿"""
        if brick_id not in self.objects:
            return None, None
        
        body_id = self.objects[brick_id]['body_id']
        position, orientation = p.getBasePositionAndOrientation(body_id)
        
        return list(position), list(orientation)
    
    def get_brick_velocity(self, brick_id: str) -> Tuple[List[float], List[float]]:
        """获取积木速度"""
        if brick_id not in self.objects:
            return None, None
        
        body_id = self.objects[brick_id]['body_id']
        linear_vel, angular_vel = p.getBaseVelocity(body_id)
        
        return list(linear_vel), list(angular_vel)
    
    def set_brick_pose(self, brick_id: str, position: List[float], orientation: float = 0):
        """设置积木位姿"""
        if brick_id not in self.objects:
            return False
        
        body_id = self.objects[brick_id]['body_id']
        quaternion = p.getQuaternionFromEuler([0, 0, np.radians(orientation)])
        
        p.resetBasePositionAndOrientation(body_id, position, quaternion)
        return True
    
    def apply_force(self, brick_id: str, force: List[float], position: List[float] = None):
        """对积木施加力"""
        if brick_id not in self.objects:
            return
        
        body_id = self.objects[brick_id]['body_id']
        
        if position is None:
            # 在质心施加力
            p.applyExternalForce(body_id, -1, force, [0, 0, 0], p.LINK_FRAME)
        else:
            # 在指定位置施加力
            p.applyExternalForce(body_id, -1, force, position, p.WORLD_FRAME)
    
    def check_collisions(self) -> List[Dict]:
        """检查碰撞"""
        collisions = []
        
        # 获取所有接触点
        contact_points = p.getContactPoints()
        
        for contact in contact_points:
            body_a = contact[1]
            body_b = contact[2]
            
            # 查找对应的积木ID
            brick_a = None
            brick_b = None
            
            for brick_id, obj_info in self.objects.items():
                if obj_info['body_id'] == body_a:
                    brick_a = brick_id
                elif obj_info['body_id'] == body_b:
                    brick_b = brick_id
            
            if brick_a and brick_b:
                collision_info = {
                    'brick_a': brick_a,
                    'brick_b': brick_b,
                    'contact_point': contact[5],  # 接触点位置
                    'contact_normal': contact[7], # 接触法向量
                    'contact_distance': contact[8], # 接触距离
                    'normal_force': contact[9]    # 法向力
                }
                collisions.append(collision_info)
        
        return collisions
    
    def calculate_system_energy(self) -> Dict[str, float]:
        """计算系统能量"""
        total_kinetic = 0.0
        total_potential = 0.0
        
        for brick_id, obj_info in self.objects.items():
            body_id = obj_info['body_id']
            
            # 获取位置和速度
            position, _ = p.getBasePositionAndOrientation(body_id)
            linear_vel, angular_vel = p.getBaseVelocity(body_id)
            
            mass = self.physics_props.get_mass()
            
            # 计算动能
            linear_ke = 0.5 * mass * np.sum(np.array(linear_vel)**2)
            
            # 计算转动动能（简化）
            inertia = self.physics_props.get_inertia()
            angular_ke = 0.5 * np.sum(np.array(inertia) * np.array(angular_vel)**2)
            
            total_kinetic += linear_ke + angular_ke
            
            # 计算势能
            height = position[2]
            potential_energy = mass * abs(self.gravity[2]) * height
            total_potential += potential_energy
        
        return {
            'kinetic_energy': total_kinetic,
            'potential_energy': total_potential,
            'total_energy': total_kinetic + total_potential
        }
    
    def analyze_stability(self) -> Dict[str, float]:
        """分析系统稳定性"""
        if not self.objects:
            return {'stability_score': 1.0, 'is_stable': True}
        
        total_velocity = 0.0
        total_angular_velocity = 0.0
        num_objects = len(self.objects)
        
        for brick_id, obj_info in self.objects.items():
            body_id = obj_info['body_id']
            linear_vel, angular_vel = p.getBaseVelocity(body_id)
            
            total_velocity += np.linalg.norm(linear_vel)
            total_angular_velocity += np.linalg.norm(angular_vel)
        
        # 计算平均速度
        avg_velocity = total_velocity / num_objects
        avg_angular_velocity = total_angular_velocity / num_objects
        
        # 稳定性评分（速度越小越稳定）
        velocity_threshold = 0.01  # m/s
        angular_threshold = 0.1    # rad/s
        
        velocity_score = np.exp(-avg_velocity / velocity_threshold)
        angular_score = np.exp(-avg_angular_velocity / angular_threshold)
        
        stability_score = (velocity_score + angular_score) / 2
        is_stable = stability_score > 0.8
        
        return {
            'stability_score': stability_score,
            'is_stable': is_stable,
            'avg_velocity': avg_velocity,
            'avg_angular_velocity': avg_angular_velocity
        }
    
    def step_simulation(self, steps: int = 1):
        """执行仿真步进"""
        for _ in range(steps):
            p.stepSimulation()
            self.step_count += 1
            
            if self.gui:
                time.sleep(self.time_step)
    
    def run_simulation(self, duration: float, real_time: bool = False):
        """运行仿真指定时间"""
        steps = int(duration / self.time_step)
        
        self.simulation_running = True
        start_time = time.time()
        
        for i in range(steps):
            if not self.simulation_running:
                break
            
            self.step_simulation(1)
            
            if real_time:
                elapsed = time.time() - start_time
                expected_time = i * self.time_step
                if elapsed < expected_time:
                    time.sleep(expected_time - elapsed)
        
        self.simulation_running = False
    
    def stop_simulation(self):
        """停止仿真"""
        self.simulation_running = False
    
    def reset_simulation(self):
        """重置仿真"""
        # 移除所有积木
        for brick_id in list(self.objects.keys()):
            self.remove_brick(brick_id)
        
        self.step_count = 0
        self.logger.info("仿真已重置")
    
    def get_simulation_info(self) -> Dict:
        """获取仿真信息"""
        energy = self.calculate_system_energy()
        stability = self.analyze_stability()
        collisions = self.check_collisions()
        
        return {
            'step_count': self.step_count,
            'simulation_time': self.step_count * self.time_step,
            'num_objects': len(self.objects),
            'energy': energy,
            'stability': stability,
            'num_collisions': len(collisions),
            'running': self.simulation_running
        }
    
    def save_state(self, filename: str):
        """保存仿真状态"""
        state_data = {
            'step_count': self.step_count,
            'objects': {}
        }
        
        for brick_id, obj_info in self.objects.items():
            position, orientation = self.get_brick_pose(brick_id)
            linear_vel, angular_vel = self.get_brick_velocity(brick_id)
            
            state_data['objects'][brick_id] = {
                'type': obj_info['type'],
                'position': position,
                'orientation': orientation,
                'linear_velocity': linear_vel,
                'angular_velocity': angular_vel
            }
        
        import json
        with open(filename, 'w') as f:
            json.dump(state_data, f, indent=2)
        
        self.logger.info(f"仿真状态已保存到: {filename}")
    
    def cleanup(self):
        """清理资源"""
        if self.physics_client is not None:
            p.disconnect(self.physics_client)
            self.physics_client = None
        
        self.logger.info("物理仿真器已清理")

def main():
    """测试物理仿真器"""
    print("⚖️ 物理仿真器测试")
    print("=" * 30)
    
    # 创建仿真器
    simulator = PhysicsSimulator(gui=True)
    
    try:
        # 创建测试积木
        print("创建测试积木...")
        
        # 底层积木
        simulator.create_brick("brick_1", [0, 0, 0.1], 0, "brick_2x4")
        simulator.create_brick("brick_2", [0.04, 0, 0.1], 0, "brick_2x4")
        
        # 上层积木
        simulator.create_brick("brick_3", [0.02, 0, 0.12], 90, "brick_2x4")
        
        # 运行仿真
        print("运行物理仿真...")
        simulator.run_simulation(duration=3.0, real_time=True)
        
        # 分析结果
        info = simulator.get_simulation_info()
        print(f"\n仿真结果:")
        print(f"  仿真步数: {info['step_count']}")
        print(f"  仿真时间: {info['simulation_time']:.2f}s")
        print(f"  总能量: {info['energy']['total_energy']:.6f}J")
        print(f"  稳定性: {info['stability']['stability_score']:.3f}")
        print(f"  碰撞数: {info['num_collisions']}")
        
        # 保存状态
        simulator.save_state('simulation_state.json')
        
        print("\n✅ 物理仿真测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    finally:
        input("按Enter键退出...")
        simulator.cleanup()

if __name__ == "__main__":
    main()
