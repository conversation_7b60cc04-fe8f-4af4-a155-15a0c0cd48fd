function trajectories = planTrajectory(yumi, brick_config, qHome)

  
% 初始化
    ik = inverseKinematics('RigidBodyTree', yumi);
    weights = [0.1, 0.1, 0.1, 1, 1, 1];
    initialGuess = qHome;

    % 設定逆運動學求解參數
   ik.SolverParameters.MaxTime = 10;
   ik.SolverParameters.MaxIterations = 3000;

    % 預設抓取深度與偏移
    %Lfinger = 0.04;  % 假設手指長度約 4cm
    offset_z = 0.05; % 提高一點避免碰撞

    n_quintic = 30; % 五次多項式段點數
    n_cartesian = 20; % Cartesian插植每段點數

    trajectories = {};
   % task_sequence = brick_config.task_sequence;
   task_sequence = brick_config.task_sequence(1:min(2,end));
    targets = brick_config.all_targets;

    % 添加調試信息
    fprintf('任務序列長度: %d\n', length(task_sequence));
    fprintf('目標數量: %d\n', size(targets, 1));

    %開始每個任務，生成7段組合軌跡
    for i = 1:length(task_sequence)
        task = task_sequence(i);
        target = targets(task.target_id, :);

        fprintf('TASK %d: %s 手臂, LEGO ID: %d\n', i, task.arm, task.arm_lego_id);

        if strcmp(task.arm, 'right')
            % 修正：確保正確訪問 cell array
            lego_type = brick_config.right_arm_initial{task.arm_lego_id, 1};
            posPick = brick_config.right_arm_initial{task.arm_lego_id, 2};
            yawPick = brick_config.right_arm_initial{task.arm_lego_id, 3};
            eeName = 'gripper_r_base'; % 右手兩手指的中間點
        else
            % 修正：確保正確訪問 cell array
            lego_type = brick_config.left_arm_initial{task.arm_lego_id, 1};
            posPick = brick_config.left_arm_initial{task.arm_lego_id, 2};
            yawPick = brick_config.left_arm_initial{task.arm_lego_id, 3};
            eeName = 'gripper_l_base'; % 左手兩手指的中間點
        end

        % 修正語法錯誤：確保 posPick 是向量格式
        if iscell(posPick)
            posPick = posPick{1};
        end  
        if iscell(yawPick)
            yawPick = yawPick{1};
        end

        % 添加調試信息
        fprintf('  LEGO-TYPE: %s\n', lego_type);
        fprintf('  PICK POSITION: [%.3f, %.3f, %.3f]\n', posPick);
        fprintf('  PICK ANGLE: %.3f\n', yawPick);

        posPlace = target(1:3);
        yawPlace = target(4);

        try
            % 定義變換矩陣
            T_home = getTransform(yumi, qHome, eeName);
            T_prePick = trvec2tform(posPick + [0 0 offset_z]) * eul2tform([0, pi, yawPick]);
            %T_pick = trvec2tform(posPick + [0 0 Lfinger]) * eul2tform([0, pi, yawPick]);
            T_pick = trvec2tform(posPick) * eul2tform([0, pi, yawPick]);
            T_liftPick = T_prePick;
            T_placeUp = trvec2tform(posPlace + [0 0 offset_z]) * eul2tform([0, pi, yawPlace]);
         
            %T_place = trvec2tform(posPlace + [0 0 Lfinger]) * eul2tform([0, pi, yawPlace]);
            T_place = trvec2tform(posPlace) * eul2tform([0, pi, yawPlace]);
            T_liftPlace = T_placeUp;

            % 逆運動學求解
            [q_home, info1] = ik(eeName, T_home, weights, qHome);
            [q_prePick, info2] = ik(eeName, T_prePick, weights, q_home);
            [q_pick, info3] = ik(eeName, T_pick, weights, q_prePick);
            [q_liftPick, info4] = ik(eeName, T_liftPick, weights, q_pick);
            [q_placeUp, info5] = ik(eeName, T_placeUp, weights, q_liftPick);
            [q_place, info6] = ik(eeName, T_place, weights, q_placeUp);
            [q_liftPlace, info7] = ik(eeName, T_liftPlace, weights, q_place);
            [q_final_home, info8] = ik(eeName, T_home, weights, q_liftPlace);

                
            % 生成軌跡
            [q1, ~, ~] = quinticpolytraj([q_home; q_prePick]', [0 1], linspace(0,1,n_quintic));
            Q_stage1 = q1';

            Q_stage2 = zeros(n_cartesian, numel(qHome));
            T_interp2 = transformtraj(T_prePick, T_pick, [0 1], linspace(0,1,n_cartesian));
            initialGuess = q_prePick;
            for j = 1:n_cartesian
                [qSol, ~] = ik(eeName, T_interp2(:,:,j), weights, initialGuess);
                Q_stage2(j,:) = qSol;
                initialGuess = qSol;
            end

            Q_stage3 = zeros(n_cartesian, numel(qHome));
            T_interp3 = transformtraj(T_pick, T_liftPick, [0 1], linspace(0,1,n_cartesian));
            initialGuess = q_pick;
            for j = 1:n_cartesian
                [qSol, ~] = ik(eeName, T_interp3(:,:,j), weights, initialGuess);
                Q_stage3(j,:) = qSol;
                initialGuess = qSol;
            end

            [q4, ~, ~] = quinticpolytraj([q_liftPick; q_placeUp]', [0 1], linspace(0,1,n_quintic));
            Q_stage4 = q4';

            Q_stage5 = zeros(n_cartesian, numel(qHome));
            T_interp5 = transformtraj(T_placeUp, T_place, [0 1], linspace(0,1,n_cartesian));
            initialGuess = q_placeUp;
            for j = 1:n_cartesian
                [qSol, ~] = ik(eeName, T_interp5(:,:,j), weights, initialGuess);
                Q_stage5(j,:) = qSol;
                initialGuess = qSol;
            end

            Q_stage6 = zeros(n_cartesian, numel(qHome));
            T_interp6 = transformtraj(T_place, T_liftPlace, [0 1], linspace(0,1,n_cartesian));
            initialGuess = q_place;
            for j = 1:n_cartesian
                [qSol, ~] = ik(eeName, T_interp6(:,:,j), weights, initialGuess);
                Q_stage6(j,:) = qSol;
                initialGuess = qSol;
            end

            [q7, ~, ~] = quinticpolytraj([q_liftPlace; q_final_home]', [0 1], linspace(0,1,n_quintic));
            Q_stage7 = q7';

            % 組合所有軌跡段
            traj.Q = [Q_stage1; Q_stage2; Q_stage3; Q_stage4; Q_stage5; Q_stage6; Q_stage7]; %將 7 段規劃的關節角合併成最終完整的軌跡traj.Q
            % 軌跡平滑化
            traj.Q_smooth = smoothdata(traj.Q, 1, 'movmean', 5);

            traj.arm = task.arm;
            traj.eeName = eeName;
            traj.pick_position = posPick;
            traj.target_position = target(1:3);
            traj.brick_type = lego_type;

            trajectories{end+1} = traj;
            
            fprintf('  ✓ 任務 %d 軌跡生成成功\n', i);
            
        catch ME
            fprintf('  ✗ 任務 %d 處理失敗: %s\n', i, ME.message);
            continue;
        end
    end
    
    fprintf('軌跡規劃完成，成功生成 %d 個軌跡\n', length(trajectories));
end