%% 第一层 LEGO 积木搭建仿真测试脚本
% 按照阶段一优先级实施第一层积木搭建仿真

clc; clear; close all;
fprintf('=== 第一层 LEGO 积木搭建仿真测试 ===\n\n');

%% 步骤1：环境验证
fprintf('步骤1：运行环境验证...\n');
try
    run('环境验证脚本.m');
    fprintf('✓ 环境验证完成\n\n');
catch ME
    fprintf('❌ 环境验证失败: %s\n', ME.message);
    return;
end

%% 步骤2：加载配置和设置环境
fprintf('步骤2：加载配置和设置环境...\n');
try
    % 机器人环境设置
    [yumi, qHome, table, ax] = setupRobotEnv();
    fprintf('✓ 机器人环境设置完成\n');
    
    % LEGO 配置
    brick_config = lego_config();
    fprintf('✓ LEGO 配置加载完成\n');
    
    % 坐标验证
    verify_coordinates(brick_config, yumi);
    fprintf('✓ 坐标验证完成\n\n');
    
catch ME
    fprintf('❌ 配置加载失败: %s\n', ME.message);
    return;
end

%% 步骤3：限制任务序列进行测试
fprintf('步骤3：准备测试任务序列...\n');
try
    % 限制为前2个任务进行测试
    test_config = brick_config;
    test_config.task_sequence = brick_config.task_sequence(1:2);
    
    fprintf('原始任务数量: %d\n', length(brick_config.task_sequence));
    fprintf('测试任务数量: %d\n', length(test_config.task_sequence));
    
    % 显示测试任务详情
    for i = 1:length(test_config.task_sequence)
        task = test_config.task_sequence(i);
        target_pos = brick_config.all_targets(task.target_id, 1:3);
        fprintf('  任务%d: %s手臂 -> 积木%s -> 目标[%.3f, %.3f, %.3f]\n', ...
            i, task.arm, task.brick_name, target_pos);
    end
    fprintf('✓ 测试任务序列准备完成\n\n');
    
catch ME
    fprintf('❌ 任务序列准备失败: %s\n', ME.message);
    return;
end

%% 步骤4：轨迹规划
fprintf('步骤4：进行轨迹规划...\n');
try
    % 使用测试配置进行轨迹规划
    trajectories = planTrajectory(yumi, test_config, qHome);
    
    if isempty(trajectories)
        error('轨迹规划返回空结果');
    end
    
    fprintf('✓ 轨迹规划成功\n');
    fprintf('生成轨迹数量: %d\n', length(trajectories));
    
    % 显示轨迹详情
    for i = 1:length(trajectories)
        traj = trajectories{i};
        fprintf('  轨迹%d: %s手臂, %d个轨迹点\n', ...
            i, traj.arm, size(traj.Q_smooth, 1));
    end
    fprintf('\n');
    
catch ME
    fprintf('❌ 轨迹规划失败: %s\n', ME.message);
    fprintf('错误位置: %s, 行 %d\n', ME.stack(1).file, ME.stack(1).line);
    
    % 尝试简化的轨迹规划
    fprintf('尝试简化的轨迹规划...\n');
    try
        simple_config = test_config;
        simple_config.task_sequence = simple_config.task_sequence(1);  % 只测试第一个任务
        trajectories = planTrajectory(yumi, simple_config, qHome);
        
        if ~isempty(trajectories)
            fprintf('✓ 简化轨迹规划成功\n');
        else
            error('简化轨迹规划也失败');
        end
    catch ME2
        fprintf('❌ 简化轨迹规划也失败: %s\n', ME2.message);
        return;
    end
end

%% 步骤5：运行 Simulink 仿真
fprintf('步骤5：运行 Simulink 仿真...\n');
try
    % 使用修复版的 runSimulink 函数
    T_total = 10;  % 仿真时间
    runSimulink_fixed(trajectories, T_total);
    
    fprintf('✓ Simulink 仿真完成\n\n');
    
catch ME
    fprintf('❌ Simulink 仿真失败: %s\n', ME.message);
    
    % 尝试诊断问题
    fprintf('尝试诊断仿真问题...\n');
    diagnose_simulink_issues();
    return;
end

%% 步骤6：结果验证和分析
fprintf('步骤6：结果验证和分析...\n');
try
    % 检查是否有仿真结果文件
    result_files = dir('simulation_result_task_*.mat');
    
    if ~isempty(result_files)
        fprintf('✓ 找到仿真结果文件: %s\n', result_files(1).name);
        
        % 加载并分析第一个结果
        load(result_files(1).name);
        if exist('simOut', 'var')
            fprintf('✓ 仿真数据加载成功\n');
            analyze_simulation_results(simOut);
        end
    else
        fprintf('⚠️  未找到仿真结果文件\n');
    end
    
catch ME
    fprintf('❌ 结果分析失败: %s\n', ME.message);
end

%% 步骤7：生成报告
fprintf('\n=== 第一层测试完成报告 ===\n');
fprintf('测试时间: %s\n', datestr(now));
fprintf('测试任务数量: %d\n', length(test_config.task_sequence));
fprintf('生成轨迹数量: %d\n', length(trajectories));

% 检查关键文件
key_results = {
    'simulation_result_task_1.mat', '第一个任务仿真结果';
    'YumiSimscape.slx', 'YuMi Simulink 模型';
};

fprintf('\n关键结果文件:\n');
for i = 1:size(key_results, 1)
    if exist(key_results{i,1}, 'file')
        fprintf('  ✓ %s\n', key_results{i,2});
    else
        fprintf('  ❌ %s (缺失)\n', key_results{i,2});
    end
end

fprintf('\n💡 下一步建议:\n');
fprintf('1. 检查 Simulink 模型的 3D 可视化窗口\n');
fprintf('2. 验证机器人动作的流畅性\n');
fprintf('3. 检查积木放置的准确性\n');
fprintf('4. 如果成功，可以增加更多测试任务\n');

fprintf('\n🎉 第一层测试脚本执行完成！\n');

%% 辅助函数
function diagnose_simulink_issues()
    fprintf('🔍 Simulink 问题诊断:\n');
    
    % 检查模型是否存在
    models = {'YumiSimscape.slx', 'Yumi_Lego_Final_Simulation.slx'};
    for i = 1:length(models)
        if exist(models{i}, 'file')
            fprintf('  ✓ %s 存在\n', models{i});
        else
            fprintf('  ❌ %s 不存在\n', models{i});
        end
    end
    
    % 检查工作空间变量
    vars = {'trajDataRight', 'trajDataLeft', 'T_total'};
    for i = 1:length(vars)
        if evalin('base', sprintf('exist(''%s'', ''var'')', vars{i}))
            fprintf('  ✓ 变量 %s 存在\n', vars{i});
        else
            fprintf('  ❌ 变量 %s 不存在\n', vars{i});
        end
    end
end

function analyze_simulation_results(simOut)
    fprintf('📊 仿真结果分析:\n');
    
    try
        % 获取仿真时间信息
        if isfield(simOut, 'tout')
            fprintf('  仿真时间: %.2f 秒\n', simOut.tout(end));
            fprintf('  时间步数: %d\n', length(simOut.tout));
        end
        
        % 检查是否有关节角度数据
        if isfield(simOut, 'yout')
            fprintf('  输出信号数量: %d\n', length(simOut.yout));
        end
        
        fprintf('  ✓ 仿真结果分析完成\n');
        
    catch ME
        fprintf('  ❌ 结果分析失败: %s\n', ME.message);
    end
end
