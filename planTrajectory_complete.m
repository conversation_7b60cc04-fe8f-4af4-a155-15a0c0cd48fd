function trajectories = planTrajectory_complete(yumi, brick_config, qHome)
% Complete trajectory planning for all 12 LEGO bricks
% This function generates trajectories for the complete first layer

% 初始化
ik = inverseKinematics('RigidBodyTree', yumi);
weights = [0.1, 0.1, 0.1, 1, 1, 1];
initialGuess = qHome;

% 設定逆運動學求解參數
ik.SolverParameters.MaxTime = 10;
ik.SolverParameters.MaxIterations = 3000;

% 預設抓取深度與偏移
offset_z = 0.05; % 提高一點避免碰撞

n_quintic = 30; % 五次多項式段點數
n_cartesian = 20; % Cartesian插植每段點數

trajectories = {};

% Use ALL task sequences - remove the limitation
task_sequence = brick_config.task_sequence;  % All 12 tasks
targets = brick_config.all_targets;

% 添加調試信息
fprintf('完整任務序列長度: %d\n', length(task_sequence));
fprintf('目標數量: %d\n', size(targets, 1));

% 分析任務分配
right_tasks = sum(strcmp({task_sequence.arm}, 'right'));
left_tasks = sum(strcmp({task_sequence.arm}, 'left'));
fprintf('任務分配: 右臂 %d 個，左臂 %d 個\n', right_tasks, left_tasks);

%開始每個任務，生成7段組合軌跡
for i = 1:length(task_sequence)
    try
        task = task_sequence(i);
        target = targets(task.target_id, :);

        fprintf('完整任務 %d: %s 手臂, LEGO ID: %d, 目標: %d\n', i, task.arm, task.arm_lego_id, task.target_id);

        if strcmp(task.arm, 'right')
            % 修正：確保正確訪問 cell array
            lego_type = brick_config.right_arm_initial{task.arm_lego_id, 1};
            posPick = brick_config.right_arm_initial{task.arm_lego_id, 2};
            yawPick = brick_config.right_arm_initial{task.arm_lego_id, 3};
            eeName = 'gripper_r_base'; % 右手兩手指的中間點
        else
            % 修正：確保正確訪問 cell array
            lego_type = brick_config.left_arm_initial{task.arm_lego_id, 1};
            posPick = brick_config.left_arm_initial{task.arm_lego_id, 2};
            yawPick = brick_config.left_arm_initial{task.arm_lego_id, 3};
            eeName = 'gripper_l_base'; % 左手兩手指的中間點
        end

        % 修正語法錯誤：確保 posPick 是向量格式
        if iscell(posPick)
            posPick = cell2mat(posPick);
        end

        fprintf('  LEGO-TYPE: %s\n', lego_type);
        fprintf('  PICK POSITION: [%.3f, %.3f, %.3f]\n', posPick);
        fprintf('  PICK ANGLE: %.3f\n', yawPick);

        % 定義7個關鍵點位置和姿態
        % 1. Home position
        posHome = [0.5, 0, 0.4];
        rotHome = eul2rotm([0, 0, 0]);

        % 2. Pre-pick position (above pick location)
        posPrePick = posPick + [0, 0, offset_z];
        rotPrePick = eul2rotm([0, pi, yawPick]);

        % 3. Pick position
        posPick_actual = posPick;
        rotPick = eul2rotm([0, pi, yawPick]);

        % 4. Post-pick position (lift up after picking)
        posPostPick = posPick + [0, 0, offset_z];
        rotPostPick = eul2rotm([0, pi, yawPick]);

        % 5. Pre-place position (above target location)
        posPrePlace = target(1:3) + [0, 0, offset_z];
        rotPrePlace = eul2rotm([0, pi, target(4)]);

        % 6. Place position
        posPlace = target(1:3);
        rotPlace = eul2rotm([0, pi, target(4)]);

        % 7. Post-place position (lift up after placing)
        posPostPlace = target(1:3) + [0, 0, offset_z];
        rotPostPlace = eul2rotm([0, pi, target(4)]);

        % 創建變換矩陣
        T_home = [rotHome, posHome'; 0, 0, 0, 1];
        T_pre_pick = [rotPrePick, posPrePick'; 0, 0, 0, 1];
        T_pick = [rotPick, posPick_actual'; 0, 0, 0, 1];
        T_post_pick = [rotPostPick, posPostPick'; 0, 0, 0, 1];
        T_pre_place = [rotPrePlace, posPrePlace'; 0, 0, 0, 1];
        T_place = [rotPlace, posPlace'; 0, 0, 0, 1];
        T_post_place = [rotPostPlace, posPostPlace'; 0, 0, 0, 1];

        % 定義7段軌跡的目標變換
        waypoints = {T_home, T_pre_pick, T_pick, T_post_pick, T_pre_place, T_place, T_post_place};

        % 生成每段軌跡
        Q_all = [];
        
        for seg = 1:length(waypoints)-1
            T_start = waypoints{seg};
            T_end = waypoints{seg+1};
            
            % 逆運動學求解起點和終點
            if seg == 1
                % 第一段從home configuration開始
                q_start_full = qHome;  % 使用完整的18關節配置
            else
                q_start_full = q_end_full;  % 使用上一段的終點
            end

            % 求解終點關節角度
            [q_end_full, solInfo] = ik(eeName, T_end, weights, q_start_full);

            % 提取對應手臂的關節角度
            if strcmp(task.arm, 'right')
                q_start = q_start_full(8:14);  % 右臂關節
                q_end = q_end_full(8:14);
            else
                q_start = q_start_full(1:7);   % 左臂關節
                q_end = q_end_full(1:7);
            end
            
            if ~strcmp(solInfo.Status, 'success')
                fprintf('  警告: 段 %d 逆運動學求解失敗\n', seg);
                % 使用近似解或跳過
                continue;
            end
            
            % 生成段軌跡
            if seg <= 3 || seg >= 6
                % 慢速段：pick和place附近
                t_seg = linspace(0, 1, n_cartesian);
            else
                % 快速段：移動段
                t_seg = linspace(0, 1, n_quintic);
            end
            
            % 五次多項式插值
            Q_seg = zeros(length(t_seg), length(q_start));
            for j = 1:length(q_start)
                Q_seg(:, j) = quintic_poly(q_start(j), q_end(j), t_seg);
            end
            
            Q_all = [Q_all; Q_seg];
        end

        % 平滑處理
        if ~isempty(Q_all)
            % 簡單的移動平均平滑
            window_size = 5;
            Q_smooth = movmean(Q_all, window_size, 1);
            
            % 創建軌跡結構
            traj = struct();
            traj.Q_smooth = Q_smooth;
            traj.arm = task.arm;
            traj.eeName = eeName;
            traj.pick_position = posPick;
            traj.target_position = target(1:3);
            traj.brick_type = lego_type;
            traj.task_id = i;
            traj.target_id = task.target_id;

            trajectories{end+1} = traj;
            
            fprintf('  ✓ 完整任務 %d 軌跡生成成功 (%d 個軌跡點)\n', i, size(Q_smooth, 1));
        else
            fprintf('  ✗ 完整任務 %d 軌跡生成失敗\n', i);
        end
        
    catch ME
        fprintf('  ✗ 完整任務 %d 處理失敗: %s\n', i, ME.message);
        continue;
    end
end

fprintf('完整軌跡規劃完成，成功生成 %d 個軌跡\n', length(trajectories));

% 最終驗證
if length(trajectories) == length(task_sequence)
    fprintf('✅ 所有 %d 個任務軌跡生成成功！\n', length(trajectories));
else
    fprintf('⚠️  只生成了 %d/%d 個軌跡\n', length(trajectories), length(task_sequence));
end

end

function y = quintic_poly(y0, yf, t)
% 五次多項式插值函數
% y0: 起始值, yf: 終止值, t: 時間向量 [0,1]

% 五次多項式係數 (假設起始和終止速度、加速度為0)
a0 = y0;
a1 = 0;
a2 = 0;
a3 = 10 * (yf - y0);
a4 = -15 * (yf - y0);
a5 = 6 * (yf - y0);

% 計算軌跡
y = a0 + a1*t + a2*t.^2 + a3*t.^3 + a4*t.^4 + a5*t.^5;

end
