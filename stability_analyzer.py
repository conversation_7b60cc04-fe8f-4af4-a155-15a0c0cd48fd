#!/usr/bin/env python3
"""
稳定性分析模块
实现LEGO城堡的多维度稳定性分析，包括重心分析、支撑分析、力平衡等

作者: AI Assistant
日期: 2025-01-26
版本: 1.0
"""

import numpy as np
import logging
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import matplotlib.pyplot as plt
from scipy.spatial import ConvexHull
import json

class StabilityRating(Enum):
    """稳定性评级枚举"""
    EXCELLENT = "excellent"    # 优秀 (>0.8)
    GOOD = "good"             # 良好 (0.6-0.8)
    FAIR = "fair"             # 一般 (0.4-0.6)
    POOR = "poor"             # 较差 (0.2-0.4)
    UNSTABLE = "unstable"     # 不稳定 (<0.2)

@dataclass
class CenterOfMassAnalysis:
    """重心分析结果"""
    system_com: np.ndarray
    support_polygon: np.ndarray
    com_in_support: bool
    safety_margin: float
    stability_score: float

@dataclass
class SupportAnalysis:
    """支撑分析结果"""
    num_layers: int
    layer_support_ratios: List[float]
    min_support_ratio: float
    avg_support_ratio: float
    support_rating: StabilityRating

@dataclass
class ForceAnalysis:
    """力分析结果"""
    total_weight: float
    tipping_resistance: float
    is_force_balanced: bool
    critical_tipping_angle: float
    force_balance_score: float

@dataclass
class GeometricAnalysis:
    """几何分析结果"""
    stack_height: float
    base_width: float
    aspect_ratio: float
    geometric_stability_score: float
    geometric_rating: StabilityRating

@dataclass
class StabilityReport:
    """稳定性报告"""
    overall_score: float
    overall_rating: StabilityRating
    is_stable: bool
    risk_factors: List[str]
    recommendations: List[str]
    component_scores: Dict[str, float]
    
    # 详细分析
    com_analysis: CenterOfMassAnalysis
    support_analysis: SupportAnalysis
    force_analysis: ForceAnalysis
    geometric_analysis: GeometricAnalysis

class StabilityAnalyzer:
    """稳定性分析器"""
    
    def __init__(self, threshold: float = 0.7):
        """初始化稳定性分析器"""
        self.threshold = threshold
        self.logger = logging.getLogger('StabilityAnalyzer')
        
        # 分析权重
        self.weights = {
            'center_of_mass': 0.4,
            'support_structure': 0.3,
            'force_balance': 0.2,
            'geometric_stability': 0.1
        }
        
        # 物理常数
        self.gravity = 9.81  # m/s²
        self.brick_mass = 0.00253  # kg (2.53g)
        self.brick_density = 1040  # kg/m³
        
        # 稳定性判据
        self.criteria = {
            'com_margin_threshold': 0.8,
            'support_ratio_threshold': 0.6,
            'tipping_angle_threshold': 15,  # degrees
            'aspect_ratio_threshold': 3.0
        }
        
        self.logger.info("稳定性分析器初始化完成")
    
    def analyze_center_of_mass(self, objects: Dict) -> CenterOfMassAnalysis:
        """分析重心"""
        if not objects:
            return CenterOfMassAnalysis(
                system_com=np.array([0, 0, 0]),
                support_polygon=np.array([]),
                com_in_support=False,
                safety_margin=0.0,
                stability_score=0.0
            )
        
        # 计算系统重心
        total_mass = 0.0
        weighted_position = np.array([0.0, 0.0, 0.0])
        
        for obj_id, obj_data in objects.items():
            mass = obj_data.get('mass', self.brick_mass)
            position = np.array(obj_data['position'])
            
            total_mass += mass
            weighted_position += mass * position
        
        system_com = weighted_position / total_mass if total_mass > 0 else np.array([0, 0, 0])
        
        # 计算支撑多边形
        support_polygon = self._calculate_support_polygon(objects)
        
        # 检查重心是否在支撑多边形内
        com_in_support = self._point_in_polygon(system_com[:2], support_polygon)
        
        # 计算安全边距
        if com_in_support and len(support_polygon) > 2:
            safety_margin = self._calculate_distance_to_boundary(system_com[:2], support_polygon)
            support_area = self._calculate_polygon_area(support_polygon)
            normalized_margin = safety_margin / np.sqrt(support_area) if support_area > 0 else 0
        else:
            normalized_margin = 0.0
        
        # 稳定性评分
        stability_score = min(1.0, normalized_margin / self.criteria['com_margin_threshold'])
        
        return CenterOfMassAnalysis(
            system_com=system_com,
            support_polygon=support_polygon,
            com_in_support=com_in_support,
            safety_margin=normalized_margin,
            stability_score=stability_score
        )
    
    def _calculate_support_polygon(self, objects: Dict) -> np.ndarray:
        """计算支撑多边形"""
        # 找到最低层的积木
        min_z = min(obj['position'][2] for obj in objects.values())
        base_objects = {k: v for k, v in objects.items() 
                       if abs(v['position'][2] - min_z) < 0.001}
        
        if not base_objects:
            return np.array([])
        
        # 收集底层积木的边界点
        boundary_points = []
        
        for obj_id, obj_data in base_objects.items():
            position = np.array(obj_data['position'])
            size = np.array(obj_data.get('size', [0.0318, 0.0159, 0.0096]))
            orientation = obj_data.get('orientation', 0.0)
            
            # 计算积木四个角点
            corners = self._get_brick_corners(position[:2], size[:2], orientation)
            boundary_points.extend(corners)
        
        if len(boundary_points) < 3:
            return np.array([])
        
        # 计算凸包
        try:
            points = np.array(boundary_points)
            hull = ConvexHull(points)
            return points[hull.vertices]
        except:
            # 如果凸包计算失败，返回边界框
            points = np.array(boundary_points)
            min_x, min_y = np.min(points, axis=0)
            max_x, max_y = np.max(points, axis=0)
            return np.array([[min_x, min_y], [max_x, min_y], 
                           [max_x, max_y], [min_x, max_y]])
    
    def _get_brick_corners(self, position: np.ndarray, size: np.ndarray, 
                          orientation: float) -> List[np.ndarray]:
        """获取积木的四个角点"""
        half_length = size[0] / 2
        half_width = size[1] / 2
        
        # 本地坐标系中的角点
        local_corners = [
            [-half_length, -half_width],
            [half_length, -half_width],
            [half_length, half_width],
            [-half_length, half_width]
        ]
        
        # 旋转变换
        cos_theta = np.cos(orientation)
        sin_theta = np.sin(orientation)
        
        global_corners = []
        for corner in local_corners:
            rotated_x = corner[0] * cos_theta - corner[1] * sin_theta
            rotated_y = corner[0] * sin_theta + corner[1] * cos_theta
            global_corner = position + np.array([rotated_x, rotated_y])
            global_corners.append(global_corner)
        
        return global_corners
    
    def _point_in_polygon(self, point: np.ndarray, polygon: np.ndarray) -> bool:
        """判断点是否在多边形内"""
        if len(polygon) < 3:
            return False
        
        x, y = point
        n = len(polygon)
        inside = False
        
        j = n - 1
        for i in range(n):
            xi, yi = polygon[i]
            xj, yj = polygon[j]
            
            if ((yi > y) != (yj > y)) and (x < (xj - xi) * (y - yi) / (yj - yi) + xi):
                inside = not inside
            j = i
        
        return inside
    
    def _calculate_distance_to_boundary(self, point: np.ndarray, polygon: np.ndarray) -> float:
        """计算点到多边形边界的最小距离"""
        if len(polygon) < 2:
            return 0.0
        
        min_distance = float('inf')
        n = len(polygon)
        
        for i in range(n):
            p1 = polygon[i]
            p2 = polygon[(i + 1) % n]
            
            distance = self._point_to_line_distance(point, p1, p2)
            min_distance = min(min_distance, distance)
        
        return min_distance
    
    def _point_to_line_distance(self, point: np.ndarray, line_start: np.ndarray, 
                               line_end: np.ndarray) -> float:
        """计算点到线段的距离"""
        A = point - line_start
        B = line_end - line_start
        
        if np.linalg.norm(B) < 1e-10:
            return np.linalg.norm(A)
        
        t = max(0, min(1, np.dot(A, B) / np.dot(B, B)))
        projection = line_start + t * B
        return np.linalg.norm(point - projection)
    
    def _calculate_polygon_area(self, polygon: np.ndarray) -> float:
        """计算多边形面积"""
        if len(polygon) < 3:
            return 0.0
        
        n = len(polygon)
        area = 0.0
        
        for i in range(n):
            j = (i + 1) % n
            area += polygon[i][0] * polygon[j][1]
            area -= polygon[j][0] * polygon[i][1]
        
        return abs(area) / 2.0
    
    def analyze_support_structure(self, objects: Dict) -> SupportAnalysis:
        """分析支撑结构"""
        if not objects:
            return SupportAnalysis(
                num_layers=0,
                layer_support_ratios=[],
                min_support_ratio=0.0,
                avg_support_ratio=0.0,
                support_rating=StabilityRating.UNSTABLE
            )
        
        # 按层分组
        layers = {}
        for obj_id, obj_data in objects.items():
            level = obj_data.get('level', 1)
            if level not in layers:
                layers[level] = []
            layers[level].append((obj_id, obj_data))
        
        layer_support_ratios = []
        
        for level in sorted(layers.keys()):
            if level == 1:
                # 底层直接支撑在地面
                layer_support_ratios.append(1.0)
            else:
                # 计算与下层的支撑比例
                current_layer = layers[level]
                lower_layer = layers.get(level - 1, [])
                
                support_ratio = self._calculate_layer_support_ratio(current_layer, lower_layer)
                layer_support_ratios.append(support_ratio)
        
        min_support_ratio = min(layer_support_ratios) if layer_support_ratios else 0.0
        avg_support_ratio = np.mean(layer_support_ratios) if layer_support_ratios else 0.0
        
        # 评级
        if min_support_ratio > 0.8:
            rating = StabilityRating.EXCELLENT
        elif min_support_ratio > 0.6:
            rating = StabilityRating.GOOD
        elif min_support_ratio > 0.4:
            rating = StabilityRating.FAIR
        elif min_support_ratio > 0.2:
            rating = StabilityRating.POOR
        else:
            rating = StabilityRating.UNSTABLE
        
        return SupportAnalysis(
            num_layers=len(layers),
            layer_support_ratios=layer_support_ratios,
            min_support_ratio=min_support_ratio,
            avg_support_ratio=avg_support_ratio,
            support_rating=rating
        )
    
    def _calculate_layer_support_ratio(self, upper_layer: List, lower_layer: List) -> float:
        """计算层间支撑比例"""
        if not upper_layer or not lower_layer:
            return 0.0
        
        total_upper_area = 0.0
        total_support_area = 0.0
        
        for obj_id, obj_data in upper_layer:
            size = np.array(obj_data.get('size', [0.0318, 0.0159, 0.0096]))
            upper_area = size[0] * size[1]
            total_upper_area += upper_area
            
            # 计算与下层积木的重叠面积
            overlap_area = 0.0
            for lower_id, lower_data in lower_layer:
                overlap = self._calculate_brick_overlap_area(obj_data, lower_data)
                overlap_area += overlap
            
            total_support_area += overlap_area
        
        return min(1.0, total_support_area / total_upper_area) if total_upper_area > 0 else 0.0
    
    def _calculate_brick_overlap_area(self, brick1: Dict, brick2: Dict) -> float:
        """计算两个积木的重叠面积"""
        pos1 = np.array(brick1['position'])
        size1 = np.array(brick1.get('size', [0.0318, 0.0159, 0.0096]))
        
        pos2 = np.array(brick2['position'])
        size2 = np.array(brick2.get('size', [0.0318, 0.0159, 0.0096]))
        
        # 简化为矩形重叠计算
        x1_min, x1_max = pos1[0] - size1[0]/2, pos1[0] + size1[0]/2
        y1_min, y1_max = pos1[1] - size1[1]/2, pos1[1] + size1[1]/2
        
        x2_min, x2_max = pos2[0] - size2[0]/2, pos2[0] + size2[0]/2
        y2_min, y2_max = pos2[1] - size2[1]/2, pos2[1] + size2[1]/2
        
        # 计算重叠区域
        overlap_x_min = max(x1_min, x2_min)
        overlap_x_max = min(x1_max, x2_max)
        overlap_y_min = max(y1_min, y2_min)
        overlap_y_max = min(y1_max, y2_max)
        
        if overlap_x_max > overlap_x_min and overlap_y_max > overlap_y_min:
            return (overlap_x_max - overlap_x_min) * (overlap_y_max - overlap_y_min)
        else:
            return 0.0
    
    def analyze_force_balance(self, objects: Dict) -> ForceAnalysis:
        """分析力平衡"""
        if not objects:
            return ForceAnalysis(
                total_weight=0.0,
                tipping_resistance=0.0,
                is_force_balanced=False,
                critical_tipping_angle=0.0,
                force_balance_score=0.0
            )
        
        # 计算总重量
        total_weight = len(objects) * self.brick_mass * self.gravity
        
        # 重心分析
        com_analysis = self.analyze_center_of_mass(objects)
        
        # 计算倾倒阻力
        if com_analysis.com_in_support and len(com_analysis.support_polygon) > 2:
            min_distance = com_analysis.safety_margin
            tipping_resistance = min_distance * total_weight
            
            # 计算临界倾倒角度
            com_height = com_analysis.system_com[2]
            if com_height > 0:
                critical_angle = np.degrees(np.arctan(min_distance / com_height))
            else:
                critical_angle = 90.0
        else:
            tipping_resistance = 0.0
            critical_angle = 0.0
        
        # 力平衡评分
        is_balanced = critical_angle > self.criteria['tipping_angle_threshold']
        balance_score = min(1.0, critical_angle / self.criteria['tipping_angle_threshold'])
        
        return ForceAnalysis(
            total_weight=total_weight,
            tipping_resistance=tipping_resistance,
            is_force_balanced=is_balanced,
            critical_tipping_angle=critical_angle,
            force_balance_score=balance_score
        )
    
    def analyze_geometric_stability(self, objects: Dict) -> GeometricAnalysis:
        """分析几何稳定性"""
        if not objects:
            return GeometricAnalysis(
                stack_height=0.0,
                base_width=0.0,
                aspect_ratio=0.0,
                geometric_stability_score=0.0,
                geometric_rating=StabilityRating.UNSTABLE
            )
        
        # 计算堆叠高度
        positions = [obj['position'] for obj in objects.values()]
        min_z = min(pos[2] for pos in positions)
        max_z = max(pos[2] for pos in positions)
        stack_height = max_z - min_z
        
        # 计算基础宽度
        com_analysis = self.analyze_center_of_mass(objects)
        if len(com_analysis.support_polygon) > 2:
            base_width = np.sqrt(self._calculate_polygon_area(com_analysis.support_polygon))
        else:
            base_width = 0.01  # 默认值
        
        # 计算高宽比
        aspect_ratio = stack_height / base_width if base_width > 0 else float('inf')
        
        # 几何稳定性评分
        if aspect_ratio < 2:
            score = 1.0
            rating = StabilityRating.EXCELLENT
        elif aspect_ratio < 3:
            score = 0.8
            rating = StabilityRating.GOOD
        elif aspect_ratio < 4:
            score = 0.6
            rating = StabilityRating.FAIR
        elif aspect_ratio < 5:
            score = 0.4
            rating = StabilityRating.POOR
        else:
            score = 0.2
            rating = StabilityRating.UNSTABLE
        
        return GeometricAnalysis(
            stack_height=stack_height,
            base_width=base_width,
            aspect_ratio=aspect_ratio,
            geometric_stability_score=score,
            geometric_rating=rating
        )
    
    def analyze_complete_stability(self, objects: Dict) -> StabilityReport:
        """完整的稳定性分析"""
        self.logger.info("开始完整稳定性分析...")
        
        # 各项分析
        com_analysis = self.analyze_center_of_mass(objects)
        support_analysis = self.analyze_support_structure(objects)
        force_analysis = self.analyze_force_balance(objects)
        geometric_analysis = self.analyze_geometric_stability(objects)
        
        # 计算综合评分
        component_scores = {
            'center_of_mass': com_analysis.stability_score,
            'support_structure': support_analysis.min_support_ratio,
            'force_balance': force_analysis.force_balance_score,
            'geometric_stability': geometric_analysis.geometric_stability_score
        }
        
        overall_score = sum(self.weights[key] * score 
                          for key, score in component_scores.items())
        
        # 确定整体评级
        if overall_score > 0.8:
            overall_rating = StabilityRating.EXCELLENT
        elif overall_score > 0.6:
            overall_rating = StabilityRating.GOOD
        elif overall_score > 0.4:
            overall_rating = StabilityRating.FAIR
        elif overall_score > 0.2:
            overall_rating = StabilityRating.POOR
        else:
            overall_rating = StabilityRating.UNSTABLE
        
        is_stable = overall_score > self.threshold
        
        # 识别风险因素
        risk_factors = []
        if not com_analysis.com_in_support:
            risk_factors.append("重心超出支撑范围")
        if support_analysis.min_support_ratio < 0.6:
            risk_factors.append("支撑不足")
        if force_analysis.critical_tipping_angle < 15:
            risk_factors.append("倾倒风险")
        if geometric_analysis.aspect_ratio > 3:
            risk_factors.append("高宽比过大")
        
        # 生成建议
        recommendations = []
        if not is_stable:
            recommendations.append("建议重新设计结构以提高稳定性")
        if com_analysis.safety_margin < 0.5:
            recommendations.append("建议扩大支撑基础")
        if support_analysis.min_support_ratio < 0.8:
            recommendations.append("建议增加层间支撑")
        
        if not recommendations:
            recommendations.append("结构稳定性良好")
        
        return StabilityReport(
            overall_score=overall_score,
            overall_rating=overall_rating,
            is_stable=is_stable,
            risk_factors=risk_factors,
            recommendations=recommendations,
            component_scores=component_scores,
            com_analysis=com_analysis,
            support_analysis=support_analysis,
            force_analysis=force_analysis,
            geometric_analysis=geometric_analysis
        )
    
    def analyze_current_structure(self) -> float:
        """分析当前结构的稳定性（简化版本）"""
        # 这是一个简化的接口，用于与主系统兼容
        return 0.8  # 返回默认稳定性评分

def main():
    """测试稳定性分析器"""
    print("⚖️ 稳定性分析器测试")
    print("=" * 30)
    
    # 创建分析器
    analyzer = StabilityAnalyzer(threshold=0.7)
    
    # 创建测试对象
    objects = {
        'brick_1': {
            'position': [0.0, 0.0, 0.065],
            'size': [0.0318, 0.0159, 0.0096],
            'level': 1,
            'mass': 0.00253
        },
        'brick_2': {
            'position': [0.032, 0.0, 0.065],
            'size': [0.0318, 0.0159, 0.0096],
            'level': 1,
            'mass': 0.00253
        },
        'brick_3': {
            'position': [0.016, 0.0, 0.075],
            'size': [0.0318, 0.0159, 0.0096],
            'level': 2,
            'mass': 0.00253
        }
    }
    
    try:
        # 执行完整分析
        print("执行稳定性分析...")
        report = analyzer.analyze_complete_stability(objects)
        
        print(f"\n稳定性分析报告:")
        print(f"  总体评分: {report.overall_score:.3f}")
        print(f"  总体评级: {report.overall_rating.value}")
        print(f"  是否稳定: {'是' if report.is_stable else '否'}")
        
        print(f"\n组件评分:")
        for component, score in report.component_scores.items():
            print(f"  {component}: {score:.3f}")
        
        print(f"\n风险因素:")
        for risk in report.risk_factors:
            print(f"  • {risk}")
        
        print(f"\n建议:")
        for rec in report.recommendations:
            print(f"  • {rec}")
        
        print("\n✅ 稳定性分析测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    main()
