# LEGO城堡搭建系统依赖包
# Python 3.7+ required

# 核心数值计算
numpy>=1.19.0
scipy>=1.5.0

# 可视化
matplotlib>=3.3.0
open3d>=0.13.0

# 物理仿真
pybullet>=3.2.0

# 图像处理（可选）
opencv-python>=4.5.0

# 数据处理
pandas>=1.1.0

# 配置管理
pyyaml>=5.4.0

# 日志和调试
colorlog>=6.0.0

# 网络通信（YuMi控制）
requests>=2.25.0
websocket-client>=1.0.0

# 数学计算
sympy>=1.8.0

# 3D几何计算
trimesh>=3.9.0

# 并发处理
concurrent-futures>=3.1.0

# 系统监控
psutil>=5.8.0

# 单元测试
pytest>=6.2.0
pytest-cov>=2.12.0

# 代码质量
flake8>=3.9.0
black>=21.0.0

# 文档生成
sphinx>=4.0.0
sphinx-rtd-theme>=0.5.0

# 开发工具
ipython>=7.20.0
jupyter>=1.0.0

# 可选：ROS支持（如果使用ROS进行机械臂控制）
# rospkg>=1.3.0
# rospy>=1.15.0

# 可选：深度学习（如果需要视觉识别）
# torch>=1.9.0
# torchvision>=0.10.0
# tensorflow>=2.6.0

# 可选：ABB机械臂SDK（需要单独安装）
# abb-robot-sdk  # 需要从ABB官方获取
