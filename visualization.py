#!/usr/bin/env python3
"""
可视化模块
实现LEGO城堡搭建过程的3D可视化和实时监控

作者: AI Assistant
日期: 2025-01-26
版本: 1.0
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from mpl_toolkits.mplot3d.art3d import Poly3DCollection
import matplotlib.animation as animation
import logging
from typing import List, Dict, Tuple, Optional
import time
import threading
from dataclasses import dataclass
import json

try:
    import open3d as o3d
    OPEN3D_AVAILABLE = True
except ImportError:
    OPEN3D_AVAILABLE = False
    print("警告: Open3D未安装，使用matplotlib进行可视化")

@dataclass
class VisualizationConfig:
    """可视化配置"""
    window_width: int = 1200
    window_height: int = 800
    background_color: Tuple[float, float, float] = (0.1, 0.1, 0.1)
    grid_enabled: bool = True
    axes_enabled: bool = True
    real_time_update: bool = True
    save_frames: bool = False
    frame_rate: int = 30

class CastleVisualizer:
    """城堡可视化器"""
    
    def __init__(self, real_time: bool = True, config: VisualizationConfig = None):
        """初始化可视化器"""
        self.real_time = real_time
        self.config = config or VisualizationConfig()
        self.logger = logging.getLogger('CastleVisualizer')
        
        # 可视化状态
        self.is_running = False
        self.current_frame = 0
        self.objects = {}
        self.build_progress = {'level': 0, 'completed': 0, 'total': 0}
        
        # 颜色映射
        self.colors = {
            'tan': [0.8, 0.6, 0.4],
            'dark_gray': [0.3, 0.3, 0.3],
            'red': [0.8, 0.2, 0.2],
            'blue': [0.2, 0.2, 0.8],
            'green': [0.2, 0.8, 0.2],
            'yellow': [0.8, 0.8, 0.2]
        }
        
        # 初始化可视化引擎
        self._initialize_visualization()
        
        self.logger.info("城堡可视化器初始化完成")
    
    def _initialize_visualization(self):
        """初始化可视化引擎"""
        if OPEN3D_AVAILABLE and self.real_time:
            self._initialize_open3d()
        else:
            self._initialize_matplotlib()
    
    def _initialize_open3d(self):
        """初始化Open3D可视化"""
        try:
            # 创建可视化窗口
            self.vis = o3d.visualization.Visualizer()
            self.vis.create_window(
                window_name="LEGO城堡搭建可视化",
                width=self.config.window_width,
                height=self.config.window_height
            )
            
            # 设置渲染选项
            render_option = self.vis.get_render_option()
            render_option.background_color = np.array(self.config.background_color)
            render_option.show_coordinate_frame = self.config.axes_enabled
            
            # 添加坐标系
            if self.config.axes_enabled:
                coordinate_frame = o3d.geometry.TriangleMesh.create_coordinate_frame(
                    size=0.1, origin=[0, 0, 0]
                )
                self.vis.add_geometry(coordinate_frame)
            
            # 添加地面网格
            if self.config.grid_enabled:
                self._add_ground_grid()
            
            self.visualization_engine = 'open3d'
            self.logger.info("✅ Open3D可视化初始化成功")
            
        except Exception as e:
            self.logger.warning(f"Open3D初始化失败: {e}, 切换到matplotlib")
            self._initialize_matplotlib()
    
    def _initialize_matplotlib(self):
        """初始化Matplotlib可视化"""
        try:
            # 创建图形和3D轴
            self.fig = plt.figure(figsize=(12, 8))
            self.ax = self.fig.add_subplot(111, projection='3d')
            
            # 设置标题和标签
            self.ax.set_title('LEGO城堡搭建可视化', fontsize=14, fontweight='bold')
            self.ax.set_xlabel('X (m)', fontsize=12)
            self.ax.set_ylabel('Y (m)', fontsize=12)
            self.ax.set_zlabel('Z (m)', fontsize=12)
            
            # 设置坐标轴范围
            self.ax.set_xlim([0.3, 0.7])
            self.ax.set_ylim([-0.2, 0.2])
            self.ax.set_zlim([0, 0.15])
            
            # 设置视角
            self.ax.view_init(elev=20, azim=45)
            
            # 添加网格
            if self.config.grid_enabled:
                self.ax.grid(True, alpha=0.3)
            
            # 设置背景色
            self.fig.patch.set_facecolor('white')
            self.ax.xaxis.pane.fill = False
            self.ax.yaxis.pane.fill = False
            self.ax.zaxis.pane.fill = False
            
            self.visualization_engine = 'matplotlib'
            self.logger.info("✅ Matplotlib可视化初始化成功")
            
        except Exception as e:
            self.logger.error(f"Matplotlib初始化失败: {e}")
            raise
    
    def _add_ground_grid(self):
        """添加地面网格"""
        if self.visualization_engine == 'open3d':
            # Open3D地面网格
            grid_size = 0.4
            grid_step = 0.05
            
            lines = []
            points = []
            
            # 创建网格线
            for i in np.arange(-grid_size/2, grid_size/2 + grid_step, grid_step):
                # X方向线
                points.extend([[i, -grid_size/2, 0], [i, grid_size/2, 0]])
                lines.append([len(points)-2, len(points)-1])
                
                # Y方向线
                points.extend([[-grid_size/2, i, 0], [grid_size/2, i, 0]])
                lines.append([len(points)-2, len(points)-1])
            
            # 创建线集
            line_set = o3d.geometry.LineSet()
            line_set.points = o3d.utility.Vector3dVector(points)
            line_set.lines = o3d.utility.Vector2iVector(lines)
            line_set.colors = o3d.utility.Vector3dVector([[0.5, 0.5, 0.5] for _ in lines])
            
            self.vis.add_geometry(line_set)
    
    def add_brick(self, brick_id: str, position: List[float], orientation: float = 0,
                  brick_type: str = "brick_2x4", color: str = "tan"):
        """添加积木到可视化"""
        try:
            if self.visualization_engine == 'open3d':
                self._add_brick_open3d(brick_id, position, orientation, brick_type, color)
            else:
                self._add_brick_matplotlib(brick_id, position, orientation, brick_type, color)
            
            # 记录积木信息
            self.objects[brick_id] = {
                'position': position,
                'orientation': orientation,
                'type': brick_type,
                'color': color
            }
            
            self.logger.debug(f"添加积木到可视化: {brick_id}")
            
        except Exception as e:
            self.logger.error(f"添加积木失败: {e}")
    
    def _add_brick_open3d(self, brick_id: str, position: List[float], 
                         orientation: float, brick_type: str, color: str):
        """使用Open3D添加积木"""
        # 根据积木类型设置尺寸
        if brick_type == "brick_2x4":
            size = [0.0318, 0.0159, 0.0096]
        elif brick_type == "brick_2x2":
            size = [0.0159, 0.0159, 0.0096]
        elif brick_type == "cone":
            # 使用圆柱体表示圆锥
            cylinder = o3d.geometry.TriangleMesh.create_cylinder(
                radius=0.008, height=0.0096
            )
            cylinder.translate(position)
            cylinder.paint_uniform_color(self.colors.get(color, [0.8, 0.6, 0.4]))
            self.vis.add_geometry(cylinder)
            return
        else:
            size = [0.0318, 0.0159, 0.0096]
        
        # 创建长方体
        box = o3d.geometry.TriangleMesh.create_box(
            width=size[0], height=size[1], depth=size[2]
        )
        
        # 移动到中心
        box.translate([-size[0]/2, -size[1]/2, -size[2]/2])
        
        # 旋转
        if orientation != 0:
            R = box.get_rotation_matrix_from_xyz((0, 0, orientation))
            box.rotate(R, center=(0, 0, 0))
        
        # 平移到目标位置
        box.translate(position)
        
        # 设置颜色
        box.paint_uniform_color(self.colors.get(color, [0.8, 0.6, 0.4]))
        
        # 计算法向量
        box.compute_vertex_normals()
        
        # 添加到可视化
        self.vis.add_geometry(box)
    
    def _add_brick_matplotlib(self, brick_id: str, position: List[float],
                             orientation: float, brick_type: str, color: str):
        """使用Matplotlib添加积木"""
        # 根据积木类型设置尺寸
        if brick_type == "brick_2x4":
            size = [0.0318, 0.0159, 0.0096]
        elif brick_type == "brick_2x2":
            size = [0.0159, 0.0159, 0.0096]
        elif brick_type == "cone":
            # 简化为小立方体
            size = [0.008, 0.008, 0.0096]
        else:
            size = [0.0318, 0.0159, 0.0096]
        
        # 创建立方体顶点
        vertices = self._create_box_vertices(position, size, orientation)
        
        # 创建面
        faces = [
            [vertices[0], vertices[1], vertices[5], vertices[4]],  # 底面
            [vertices[2], vertices[3], vertices[7], vertices[6]],  # 顶面
            [vertices[0], vertices[1], vertices[2], vertices[3]],  # 前面
            [vertices[4], vertices[5], vertices[6], vertices[7]],  # 后面
            [vertices[0], vertices[3], vertices[7], vertices[4]],  # 左面
            [vertices[1], vertices[2], vertices[6], vertices[5]]   # 右面
        ]
        
        # 添加到3D图形
        brick_color = self.colors.get(color, [0.8, 0.6, 0.4])
        poly3d = Poly3DCollection(faces, alpha=0.8, facecolor=brick_color, edgecolor='black')
        self.ax.add_collection3d(poly3d)
        
        # 添加标签
        self.ax.text(position[0], position[1], position[2] + size[2]/2 + 0.005,
                    brick_id, fontsize=8, ha='center')
    
    def _create_box_vertices(self, position: List[float], size: List[float], 
                           orientation: float) -> List[List[float]]:
        """创建立方体顶点"""
        # 本地坐标系中的顶点
        half_size = [s/2 for s in size]
        local_vertices = [
            [-half_size[0], -half_size[1], -half_size[2]],  # 0
            [half_size[0], -half_size[1], -half_size[2]],   # 1
            [half_size[0], half_size[1], -half_size[2]],    # 2
            [-half_size[0], half_size[1], -half_size[2]],   # 3
            [-half_size[0], -half_size[1], half_size[2]],   # 4
            [half_size[0], -half_size[1], half_size[2]],    # 5
            [half_size[0], half_size[1], half_size[2]],     # 6
            [-half_size[0], half_size[1], half_size[2]]     # 7
        ]
        
        # 旋转和平移
        cos_theta = np.cos(orientation)
        sin_theta = np.sin(orientation)
        
        global_vertices = []
        for vertex in local_vertices:
            # 旋转
            rotated_x = vertex[0] * cos_theta - vertex[1] * sin_theta
            rotated_y = vertex[0] * sin_theta + vertex[1] * cos_theta
            rotated_z = vertex[2]
            
            # 平移
            global_vertex = [
                position[0] + rotated_x,
                position[1] + rotated_y,
                position[2] + rotated_z
            ]
            global_vertices.append(global_vertex)
        
        return global_vertices
    
    def remove_brick(self, brick_id: str):
        """从可视化中移除积木"""
        if brick_id in self.objects:
            del self.objects[brick_id]
            self.logger.debug(f"从可视化中移除积木: {brick_id}")
            
            # 对于matplotlib，需要重新绘制
            if self.visualization_engine == 'matplotlib':
                self._redraw_matplotlib()
    
    def _redraw_matplotlib(self):
        """重新绘制matplotlib场景"""
        self.ax.clear()
        
        # 重新设置轴
        self.ax.set_xlabel('X (m)')
        self.ax.set_ylabel('Y (m)')
        self.ax.set_zlabel('Z (m)')
        self.ax.set_xlim([0.3, 0.7])
        self.ax.set_ylim([-0.2, 0.2])
        self.ax.set_zlim([0, 0.15])
        
        # 重新添加所有积木
        for brick_id, brick_data in self.objects.items():
            self._add_brick_matplotlib(
                brick_id,
                brick_data['position'],
                brick_data['orientation'],
                brick_data['type'],
                brick_data['color']
            )
    
    def update_progress(self, level: int, completed: int, total: int):
        """更新搭建进度"""
        self.build_progress = {
            'level': level,
            'completed': completed,
            'total': total
        }
        
        # 更新标题
        progress_percent = (completed / total * 100) if total > 0 else 0
        title = f"LEGO城堡搭建 - Level {level} - 进度: {progress_percent:.1f}% ({completed}/{total})"
        
        if self.visualization_engine == 'matplotlib':
            self.ax.set_title(title, fontsize=14, fontweight='bold')
            plt.draw()
            plt.pause(0.01)
    
    def add_center_of_mass(self, position: List[float]):
        """添加重心标记"""
        if self.visualization_engine == 'open3d':
            # 创建重心球体
            sphere = o3d.geometry.TriangleMesh.create_sphere(radius=0.005)
            sphere.translate(position)
            sphere.paint_uniform_color([1.0, 0.0, 0.0])  # 红色
            self.vis.add_geometry(sphere)
        else:
            # matplotlib重心标记
            self.ax.scatter(position[0], position[1], position[2],
                          c='red', s=100, marker='o', label='重心')
    
    def add_support_polygon(self, polygon: np.ndarray, height: float = 0.001):
        """添加支撑多边形"""
        if len(polygon) < 3:
            return
        
        if self.visualization_engine == 'matplotlib':
            # 创建多边形
            polygon_3d = np.column_stack([polygon, np.full(len(polygon), height)])
            
            # 添加多边形面
            poly3d = Poly3DCollection([polygon_3d], alpha=0.3, facecolor='green')
            self.ax.add_collection3d(poly3d)
            
            # 添加边界线
            for i in range(len(polygon)):
                j = (i + 1) % len(polygon)
                self.ax.plot([polygon[i][0], polygon[j][0]],
                           [polygon[i][1], polygon[j][1]],
                           [height, height], 'g-', linewidth=2)
    
    def show(self):
        """显示可视化"""
        if self.visualization_engine == 'open3d':
            self.vis.run()
        else:
            plt.show()
    
    def update(self):
        """更新可视化（用于实时显示）"""
        if self.visualization_engine == 'open3d':
            self.vis.poll_events()
            self.vis.update_renderer()
        else:
            plt.draw()
            plt.pause(0.01)
    
    def save_screenshot(self, filename: str):
        """保存截图"""
        try:
            if self.visualization_engine == 'open3d':
                self.vis.capture_screen_image(filename)
            else:
                plt.savefig(filename, dpi=300, bbox_inches='tight')
            
            self.logger.info(f"截图已保存: {filename}")
            
        except Exception as e:
            self.logger.error(f"保存截图失败: {e}")
    
    def close(self):
        """关闭可视化"""
        try:
            if self.visualization_engine == 'open3d':
                self.vis.destroy_window()
            else:
                plt.close(self.fig)
            
            self.logger.info("可视化已关闭")
            
        except Exception as e:
            self.logger.error(f"关闭可视化失败: {e}")

def main():
    """测试可视化器"""
    print("🎨 城堡可视化器测试")
    print("=" * 30)
    
    # 创建可视化器
    visualizer = CastleVisualizer(real_time=True)
    
    try:
        # 添加测试积木
        print("添加测试积木...")
        
        # 底层积木
        visualizer.add_brick("brick_1", [0.5, -0.02, 0.065], 0, "brick_2x4", "tan")
        visualizer.add_brick("brick_2", [0.5, 0.02, 0.065], 0, "brick_2x4", "tan")
        
        # 中层积木
        visualizer.add_brick("brick_3", [0.5, 0, 0.075], 90, "brick_2x4", "tan")
        
        # 顶层圆锥
        visualizer.add_brick("cone_1", [0.5, 0, 0.085], 0, "cone", "dark_gray")
        
        # 添加重心标记
        visualizer.add_center_of_mass([0.5, 0, 0.075])
        
        # 添加支撑多边形
        support_polygon = np.array([
            [0.48, -0.04],
            [0.52, -0.04],
            [0.52, 0.04],
            [0.48, 0.04]
        ])
        visualizer.add_support_polygon(support_polygon)
        
        # 更新进度
        visualizer.update_progress(level=3, completed=4, total=10)
        
        print("显示可视化...")
        
        # 实时更新演示
        if visualizer.visualization_engine == 'matplotlib':
            for i in range(50):
                visualizer.update()
                time.sleep(0.1)
        
        # 保存截图
        visualizer.save_screenshot('castle_visualization_test.png')
        
        print("✅ 可视化测试完成")
        
        # 显示可视化
        if visualizer.visualization_engine == 'open3d':
            visualizer.show()
        else:
            input("按Enter键关闭可视化...")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    finally:
        visualizer.close()

if __name__ == "__main__":
    main()
