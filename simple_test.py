#!/usr/bin/env python3
"""
LEGO城堡搭建系统简化测试脚本
验证核心模块功能（不依赖外部库）

作者: AI Assistant
日期: 2025-01-26
版本: 1.0
"""

import sys
import os
import json
import time
import numpy as np

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_castle_structure():
    """测试城堡结构定义"""
    print("🏰 测试城堡结构定义...")
    
    try:
        from castle_structure import CastleStructureDefinition
        
        # 创建城堡结构
        castle = CastleStructureDefinition()
        
        # 基本验证
        total_bricks = castle.get_total_brick_count()
        total_levels = castle.get_level_count()
        
        print(f"   ✓ 总积木数: {total_bricks}")
        print(f"   ✓ 总层数: {total_levels}")
        
        # 验证各层
        for level in range(1, total_levels + 1):
            level_bricks = castle.get_level_bricks(level)
            print(f"   ✓ Level {level}: {len(level_bricks)} 个积木")
        
        # 结构验证
        if castle.validate_structure():
            print("   ✅ 城堡结构验证通过")
            return True
        else:
            print("   ❌ 城堡结构验证失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def test_yumi_controller():
    """测试YuMi控制器（模拟模式）"""
    print("\n🤖 测试YuMi控制器...")
    
    try:
        from yumi_controller import YuMiDualArmController, CartesianPose
        
        # 创建控制器（使用本地IP进行模拟）
        controller = YuMiDualArmController(
            ip_address="127.0.0.1",
            left_enabled=True,
            right_enabled=True
        )
        
        print(f"   ✓ 控制器创建成功")
        print(f"   ✓ 连接状态: {controller.is_connected()}")
        
        # 测试位姿操作
        test_pose = CartesianPose(x=0.5, y=0.0, z=0.1, rx=180, ry=0, rz=0)
        pose_list = test_pose.to_list()
        print(f"   ✓ 位姿转换: {pose_list}")
        
        # 测试夹爪操作
        left_open = controller.open_gripper("left")
        right_open = controller.open_gripper("right")
        print(f"   ✓ 夹爪操作: 左臂={left_open}, 右臂={right_open}")
        
        # 测试状态获取
        status = controller.get_status()
        print(f"   ✓ 状态获取: {len(status)} 个状态项")
        
        controller.disconnect()
        print("   ✅ YuMi控制器测试通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def test_collision_detection():
    """测试碰撞检测"""
    print("\n🔍 测试碰撞检测...")
    
    try:
        from collision_detection import CollisionDetector, AABB
        
        # 创建检测器
        detector = CollisionDetector(tolerance=1e-4)
        print(f"   ✓ 检测器创建成功，容差: {detector.tolerance}")
        
        # 测试AABB
        aabb1 = AABB(np.array([0, 0, 0]), np.array([1, 1, 1]))
        aabb2 = AABB(np.array([0.5, 0.5, 0.5]), np.array([1.5, 1.5, 1.5]))
        
        overlap = aabb1.overlaps(aabb2)
        print(f"   ✓ AABB重叠检测: {overlap}")
        
        # 测试对象碰撞检测
        objects = {
            'brick1': {
                'position': [0, 0, 0],
                'orientation': 0,
                'size': [0.1, 0.1, 0.1]
            },
            'brick2': {
                'position': [0.05, 0, 0],  # 部分重叠
                'orientation': 0,
                'size': [0.1, 0.1, 0.1]
            }
        }
        
        collisions = detector.check_collisions(objects)
        print(f"   ✓ 碰撞检测结果: {len(collisions)} 个碰撞")
        
        # 性能统计
        stats = detector.get_performance_stats()
        print(f"   ✓ 性能统计: {stats['total_detections']} 次检测")
        
        print("   ✅ 碰撞检测测试通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def test_stability_analyzer():
    """测试稳定性分析"""
    print("\n⚖️ 测试稳定性分析...")
    
    try:
        from stability_analyzer import StabilityAnalyzer
        
        # 创建分析器
        analyzer = StabilityAnalyzer(threshold=0.7)
        print(f"   ✓ 分析器创建成功，阈值: {analyzer.threshold}")
        
        # 测试对象
        objects = {
            'brick1': {
                'position': [0, 0, 0.065],
                'level': 1,
                'mass': 0.00253,
                'size': [0.0318, 0.0159, 0.0096]
            },
            'brick2': {
                'position': [0.032, 0, 0.065],
                'level': 1,
                'mass': 0.00253,
                'size': [0.0318, 0.0159, 0.0096]
            },
            'brick3': {
                'position': [0.016, 0, 0.075],
                'level': 2,
                'mass': 0.00253,
                'size': [0.0318, 0.0159, 0.0096]
            }
        }
        
        # 重心分析
        com_analysis = analyzer.analyze_center_of_mass(objects)
        print(f"   ✓ 重心分析: COM={com_analysis.system_com}, 稳定性={com_analysis.stability_score:.3f}")
        
        # 支撑分析
        support_analysis = analyzer.analyze_support_structure(objects)
        print(f"   ✓ 支撑分析: {support_analysis.num_layers}层, 评级={support_analysis.support_rating.value}")
        
        # 完整分析
        report = analyzer.analyze_complete_stability(objects)
        print(f"   ✓ 完整分析: 评分={report.overall_score:.3f}, 稳定={report.is_stable}")
        print(f"   ✓ 风险因素: {len(report.risk_factors)}个")
        print(f"   ✓ 建议: {len(report.recommendations)}条")
        
        print("   ✅ 稳定性分析测试通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def test_visualization():
    """测试可视化（简化版）"""
    print("\n🎨 测试可视化系统...")
    
    try:
        from visualization import CastleVisualizer
        
        # 创建可视化器（非实时模式）
        visualizer = CastleVisualizer(real_time=False)
        print(f"   ✓ 可视化器创建成功，引擎: {visualizer.visualization_engine}")
        
        # 添加测试积木
        visualizer.add_brick("test_brick", [0.5, 0, 0.065], 0, "brick_2x4", "tan")
        print(f"   ✓ 积木添加成功，当前对象数: {len(visualizer.objects)}")
        
        # 更新进度
        visualizer.update_progress(level=1, completed=1, total=43)
        progress = visualizer.build_progress
        print(f"   ✓ 进度更新: Level {progress['level']}, {progress['completed']}/{progress['total']}")
        
        # 清理
        visualizer.close()
        print("   ✅ 可视化系统测试通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def test_system_integration():
    """测试系统集成"""
    print("\n🔧 测试系统集成...")
    
    try:
        from lego_castle_builder import BuildConfig
        
        # 测试配置
        config = BuildConfig(
            yumi_ip="127.0.0.1",
            enable_visualization=False
        )
        print(f"   ✓ 配置创建成功: IP={config.yumi_ip}")
        
        # 测试配置序列化
        config_dict = {
            'yumi_ip': config.yumi_ip,
            'left_arm_enabled': config.left_arm_enabled,
            'brick_mass': config.brick_mass,
            'stability_threshold': config.stability_threshold
        }
        
        config_json = json.dumps(config_dict, indent=2)
        print(f"   ✓ 配置序列化成功: {len(config_json)} 字符")
        
        print("   ✅ 系统集成测试通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def test_performance():
    """性能测试"""
    print("\n🚀 性能测试...")
    
    try:
        from collision_detection import CollisionDetector
        
        # 碰撞检测性能
        detector = CollisionDetector()
        
        # 创建测试对象
        objects = {}
        for i in range(50):
            objects[f'brick_{i}'] = {
                'position': [i * 0.01, 0, 0],
                'orientation': 0,
                'size': [0.0318, 0.0159, 0.0096]
            }
        
        start_time = time.time()
        collisions = detector.check_collisions(objects)
        detection_time = time.time() - start_time
        
        print(f"   ✓ 50个对象碰撞检测: {detection_time:.4f}s")
        print(f"   ✓ 检测到碰撞: {len(collisions)}个")
        
        # 稳定性分析性能
        from stability_analyzer import StabilityAnalyzer
        analyzer = StabilityAnalyzer()
        
        test_objects = {
            f'brick_{i}': {
                'position': [i * 0.032, 0, 0.065],
                'level': 1,
                'mass': 0.00253,
                'size': [0.0318, 0.0159, 0.0096]
            }
            for i in range(10)
        }
        
        start_time = time.time()
        report = analyzer.analyze_complete_stability(test_objects)
        analysis_time = time.time() - start_time
        
        print(f"   ✓ 10个对象稳定性分析: {analysis_time:.4f}s")
        print(f"   ✓ 稳定性评分: {report.overall_score:.3f}")
        
        print("   ✅ 性能测试通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 LEGO城堡搭建系统简化测试")
    print("=" * 50)
    
    # 测试列表
    tests = [
        ("城堡结构定义", test_castle_structure),
        ("YuMi控制器", test_yumi_controller),
        ("碰撞检测", test_collision_detection),
        ("稳定性分析", test_stability_analyzer),
        ("可视化系统", test_visualization),
        ("系统集成", test_system_integration),
        ("性能测试", test_performance)
    ]
    
    # 运行测试
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 测试: {test_name}")
        try:
            if test_func():
                passed += 1
            else:
                print(f"   ❌ {test_name} 测试失败")
        except Exception as e:
            print(f"   ❌ {test_name} 测试异常: {e}")
    
    # 输出结果
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统功能正常")
        print("\n✅ 系统准备就绪，可以开始LEGO城堡搭建")
        
        # 显示下一步操作
        print("\n📋 下一步操作:")
        print("1. 安装依赖包: pip install -r requirements.txt")
        print("2. 配置YuMi IP: python run_castle_builder.py --create-config")
        print("3. 运行仿真: python run_castle_builder.py --simulation")
        print("4. 运行真实搭建: python run_castle_builder.py --real")
        
        return True
    else:
        print(f"❌ {total - passed} 个测试失败，请检查系统配置")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
