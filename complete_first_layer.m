%% Complete First Layer LEGO Building - All 12 Bricks
% This script implements the complete first layer with all 12 LEGO bricks

function complete_first_layer()
    clc; clear; close all;
    fprintf('=== Complete First Layer LEGO Building (12 Bricks) ===\n\n');
    
    %% 1. Setup and Configuration
    fprintf('1. Loading complete configuration...\n');
    try
        % Load robot and configuration
        yumi = loadrobot('abbYumi', 'DataFormat', 'row');
        qHome = yumi.homeConfiguration;
        brick_config = lego_config();
        
        % Use ALL 12 tasks for complete first layer
        complete_config = brick_config;
        % Remove the limitation - use all tasks
        complete_config.task_sequence = brick_config.task_sequence;  % All 12 tasks
        
        fprintf('   ✓ YuMi robot loaded (%d joints)\n', length(qHome));
        fprintf('   ✓ Complete first layer: %d tasks\n', length(complete_config.task_sequence));
        
        % Analyze task distribution
        right_tasks = sum(strcmp({complete_config.task_sequence.arm}, 'right'));
        left_tasks = sum(strcmp({complete_config.task_sequence.arm}, 'left'));
        fprintf('   ✓ Task distribution: Right arm %d, Left arm %d\n', right_tasks, left_tasks);
        
    catch ME
        fprintf('   ❌ Setup failed: %s\n', ME.message);
        return;
    end
    
    %% 2. Generate Complete Trajectories
    fprintf('\n2. Generating complete trajectories for all 12 bricks...\n');
    try
        % Modify planTrajectory to handle all tasks
        trajectories = planTrajectory_complete(yumi, complete_config, qHome);
        
        if ~isempty(trajectories)
            fprintf('   ✓ Generated %d complete trajectories\n', length(trajectories));
            
            % Detailed analysis
            right_count = 0;
            left_count = 0;
            for i = 1:length(trajectories)
                traj = trajectories{i};
                task = complete_config.task_sequence(i);
                target_pos = brick_config.all_targets(task.target_id, 1:3);
                
                if strcmp(traj.arm, 'right')
                    right_count = right_count + 1;
                    arm_symbol = '🔴';
                else
                    left_count = left_count + 1;
                    arm_symbol = '🟢';
                end
                
                fprintf('     %s Task %d: %s arm, %d waypoints → Target %d\n', ...
                        arm_symbol, i, traj.arm, size(traj.Q_smooth, 1), task.target_id);
            end
            
            fprintf('   ✓ Final distribution: Right %d, Left %d\n', right_count, left_count);
        else
            error('Complete trajectory generation failed');
        end
        
    catch ME
        fprintf('   ❌ Complete trajectory generation failed: %s\n', ME.message);
        return;
    end
    
    %% 3. Create Complete Visualization Environment
    fprintf('\n3. Creating complete visualization environment...\n');
    try
        % Create comprehensive figure
        fig = figure('Name', 'Complete First Layer LEGO Building (12 Bricks)', ...
                     'Position', [50, 50, 1800, 1000], ...
                     'Color', [0.95, 0.95, 0.95]);
        
        % Main 3D view
        ax_main = subplot(2, 4, [1, 2, 5, 6], 'Parent', fig);
        hold(ax_main, 'on');
        grid(ax_main, 'on');
        axis(ax_main, 'equal');
        xlabel(ax_main, 'X (m)', 'FontSize', 12);
        ylabel(ax_main, 'Y (m)', 'FontSize', 12);
        zlabel(ax_main, 'Z (m)', 'FontSize', 12);
        title(ax_main, 'Complete First Layer Building', 'FontSize', 14, 'FontWeight', 'bold');
        view(ax_main, 45, 30);
        
        % Top view
        ax_top = subplot(2, 4, 3, 'Parent', fig);
        hold(ax_top, 'on');
        grid(ax_top, 'on');
        axis(ax_top, 'equal');
        title(ax_top, 'Top View - Building Layout', 'FontSize', 12);
        xlabel(ax_top, 'X (m)');
        ylabel(ax_top, 'Y (m)');
        
        % Progress tracking
        ax_progress = subplot(2, 4, 4, 'Parent', fig);
        title(ax_progress, 'Building Progress', 'FontSize', 12);
        
        % Task sequence view
        ax_sequence = subplot(2, 4, 7, 'Parent', fig);
        axis(ax_sequence, 'off');
        title(ax_sequence, 'Task Sequence', 'FontSize', 12);
        
        % Status display
        ax_status = subplot(2, 4, 8, 'Parent', fig);
        axis(ax_status, 'off');
        title(ax_status, 'Current Status', 'FontSize', 12);
        
        fprintf('   ✓ Complete visualization environment created\n');
        
    catch ME
        fprintf('   ❌ Complete visualization setup failed: %s\n', ME.message);
        return;
    end
    
    %% 4. Setup Complete Static Elements
    fprintf('\n4. Setting up complete static elements...\n');
    try
        % Get all positions
        targets = brick_config.all_targets;
        right_positions = cell2mat(cellfun(@(x) x, brick_config.right_arm_initial(:,2), 'UniformOutput', false));
        left_positions = cell2mat(cellfun(@(x) x, brick_config.left_arm_initial(:,2), 'UniformOutput', false));
        
        % Plot in main 3D view
        target_scatter = scatter3(ax_main, targets(:,1), targets(:,2), targets(:,3), ...
                                 120, 'r', 'o', 'LineWidth', 2, 'DisplayName', 'All Targets');
        right_scatter = scatter3(ax_main, right_positions(:,1), right_positions(:,2), right_positions(:,3), ...
                                100, 'b', 'filled', 'MarkerEdgeColor', 'k', 'DisplayName', 'Right Arm Bricks');
        left_scatter = scatter3(ax_main, left_positions(:,1), left_positions(:,2), left_positions(:,3), ...
                               100, 'g', 'filled', 'MarkerEdgeColor', 'k', 'DisplayName', 'Left Arm Bricks');
        
        % Enhanced workspace
        workspace_x = [0.35, 0.65, 0.65, 0.35, 0.35];
        workspace_y = [-0.1, -0.1, 0.1, 0.1, -0.1];
        workspace_z = [0.06, 0.06, 0.06, 0.06, 0.06];
        plot3(ax_main, workspace_x, workspace_y, workspace_z, 'k--', 'LineWidth', 3);
        
        % Plot in top view with brick numbers
        scatter(ax_top, targets(:,1), targets(:,2), 120, 'r', 'o', 'LineWidth', 2);
        scatter(ax_top, right_positions(:,1), right_positions(:,2), 100, 'b', 'filled');
        scatter(ax_top, left_positions(:,1), left_positions(:,2), 100, 'g', 'filled');
        
        % Add target numbers
        for i = 1:size(targets, 1)
            text(ax_top, targets(i,1), targets(i,2), sprintf('T%d', i), ...
                 'HorizontalAlignment', 'center', 'FontSize', 8, 'FontWeight', 'bold');
        end
        
        % Add brick numbers
        for i = 1:size(right_positions, 1)
            text(ax_top, right_positions(i,1), right_positions(i,2), sprintf('R%d', i), ...
                 'HorizontalAlignment', 'center', 'FontSize', 8, 'Color', 'white', 'FontWeight', 'bold');
        end
        for i = 1:size(left_positions, 1)
            text(ax_top, left_positions(i,1), left_positions(i,2), sprintf('L%d', i), ...
                 'HorizontalAlignment', 'center', 'FontSize', 8, 'Color', 'white', 'FontWeight', 'bold');
        end
        
        % Show robot in home position
        show(yumi, qHome, 'Parent', ax_main, 'Visuals', 'on', 'Collision', 'off');
        
        % Setup progress tracking
        progress_x = 1:length(trajectories);
        progress_y = zeros(1, length(trajectories));
        progress_bar = bar(ax_progress, progress_x, progress_y, 'FaceColor', [0.8, 0.8, 0.8]);
        set(ax_progress, 'XLim', [0.5, length(trajectories)+0.5], 'YLim', [0, 1]);
        xlabel(ax_progress, 'Task Number');
        ylabel(ax_progress, 'Completion');
        
        fprintf('   ✓ Complete static elements added\n');
        
    catch ME
        fprintf('   ❌ Complete static elements failed: %s\n', ME.message);
    end
    
    %% 5. Execute Complete Building Sequence
    fprintf('\n5. Executing complete building sequence...\n');
    
    % Animation parameters for complete sequence
    animation_params = struct();
    animation_params.frame_rate = 20;  % Slightly lower for 12 tasks
    animation_params.time_per_task = 6;  % Reduced time per task
    animation_params.pause_between_tasks = 1;  % Shorter pause
    animation_params.show_trails = true;
    
    completed_tasks = [];
    total_start_time = tic;
    
    try
        for traj_idx = 1:length(trajectories)
            task_start_time = tic;
            
            traj = trajectories{traj_idx};
            task = complete_config.task_sequence(traj_idx);
            target_pos = targets(task.target_id, 1:3);
            
            fprintf('   🔄 Executing Task %d/%d: %s arm → Target %d\n', ...
                    traj_idx, length(trajectories), traj.arm, task.target_id);
            
            % Update task sequence display
            sequence_text = cell(length(trajectories), 1);
            for j = 1:length(trajectories)
                seq_task = complete_config.task_sequence(j);
                if j < traj_idx
                    status_symbol = '✅';
                elseif j == traj_idx
                    status_symbol = '🔄';
                else
                    status_symbol = '⏳';
                end
                
                if strcmp(seq_task.arm, 'right')
                    arm_symbol = '🔴';
                else
                    arm_symbol = '🟢';
                end
                sequence_text{j} = sprintf('%s %s T%d: %s→T%d', ...
                                          status_symbol, arm_symbol, j, seq_task.arm, seq_task.target_id);
            end
            
            cla(ax_sequence);
            text(ax_sequence, 0.05, 0.95, sequence_text, 'FontSize', 8, ...
                 'VerticalAlignment', 'top', 'Units', 'normalized');
            
            % Simplified animation for 12 tasks
            Q = traj.Q_smooth;
            num_frames = animation_params.frame_rate * animation_params.time_per_task;
            frame_indices = round(linspace(1, size(Q, 1), min(num_frames, 50)));  % Limit frames
            
            for frame_idx = 1:length(frame_indices)
                i = frame_indices(frame_idx);
                q_current = Q(i, :);
                
                % Update robot configuration
                q_full = qHome;
                if strcmp(traj.arm, 'right')
                    if length(q_current) == 7 && length(q_full) >= 14
                        q_full(8:14) = q_current;
                    end
                else
                    if length(q_current) == 7
                        q_full(1:7) = q_current;
                    end
                end
                
                % Update robot visualization (simplified for performance)
                if mod(frame_idx, 5) == 1  % Update every 5th frame for performance
                    try
                        % Clear robot visualization
                        delete(findobj(ax_main, 'Type', 'Line', 'Tag', 'RobotVisualization'));
                        delete(findobj(ax_main, 'Type', 'Patch', 'Tag', 'RobotVisualization'));
                        
                        % Show robot
                        show(yumi, q_full, 'Parent', ax_main, 'Visuals', 'on', 'Collision', 'off');
                        
                        % Update progress bar
                        progress_y = zeros(1, length(trajectories));
                        progress_y(1:traj_idx-1) = 1;  % Completed tasks
                        progress_y(traj_idx) = frame_idx / length(frame_indices);  % Current task
                        set(progress_bar, 'YData', progress_y);
                        
                        % Update status
                        elapsed_time = toc(total_start_time);
                        task_progress = frame_idx / length(frame_indices) * 100;
                        
                        status_text = {
                            sprintf('Task %d/%d: %s Arm', traj_idx, length(trajectories), upper(traj.arm));
                            sprintf('Progress: %.1f%%', task_progress);
                            sprintf('Completed: %d/%d', length(completed_tasks), length(trajectories));
                            '';
                            sprintf('Total Time: %.1f s', elapsed_time);
                            sprintf('Current Target: %d', task.target_id);
                            sprintf('Brick Type: %s', task.brick_name);
                        };
                        
                        cla(ax_status);
                        text(ax_status, 0.1, 0.9, status_text, 'FontSize', 10, ...
                             'VerticalAlignment', 'top', 'Units', 'normalized');
                        
                        drawnow;
                    catch
                        % Continue if visualization fails
                        continue;
                    end
                end
                
                % Frame rate control
                pause(1 / animation_params.frame_rate);
            end
            
            % Mark task as completed
            completed_tasks(end+1) = task.target_id;
            
            % Update top view with completed brick
            scatter(ax_top, target_pos(1), target_pos(2), 150, 'g', 'filled', 'MarkerEdgeColor', 'k');
            text(ax_top, target_pos(1), target_pos(2), sprintf('✓%d', task.target_id), ...
                 'HorizontalAlignment', 'center', 'FontSize', 8, 'Color', 'white', 'FontWeight', 'bold');
            
            task_time = toc(task_start_time);
            fprintf('     ✅ Task %d completed in %.1f seconds\n', traj_idx, task_time);
            
            % Brief pause between tasks
            pause(animation_params.pause_between_tasks);
        end
        
        total_time = toc(total_start_time);
        fprintf('   🎉 All 12 tasks completed in %.1f seconds!\n', total_time);
        
    catch ME
        fprintf('   ❌ Complete building sequence failed: %s\n', ME.message);
        fprintf('   Error details: %s\n', ME.message);
    end
    
    %% 6. Final Complete Summary
    fprintf('\n=== Complete First Layer Building Summary ===\n');
    
    % Update final displays
    final_status = {
        '🎉 COMPLETE FIRST LAYER BUILT!';
        '';
        sprintf('Total Tasks: %d/%d ✅', length(completed_tasks), length(trajectories));
        sprintf('Right Arm Tasks: %d', right_count);
        sprintf('Left Arm Tasks: %d', left_count);
        sprintf('Total Time: %.1f seconds', total_time);
        '';
        '🏗️ First Layer Foundation Complete!';
        '';
        'Ready for:';
        '• 3D Brick Visualization';
        '• Gripper Control';
        '• Layer 2 Planning';
    };
    
    cla(ax_status);
    text(ax_status, 0.1, 0.9, final_status, 'FontSize', 10, ...
         'VerticalAlignment', 'top', 'Units', 'normalized', ...
         'FontWeight', 'bold', 'Color', 'green');
    
    % Update main title
    sgtitle(fig, sprintf('Complete First Layer LEGO Building - %d/%d BRICKS PLACED!', ...
                        length(completed_tasks), length(trajectories)), ...
            'FontSize', 16, 'FontWeight', 'bold', 'Color', 'green');
    
    fprintf('Complete Building Results:\n');
    fprintf('  ✅ Total bricks placed: %d/%d\n', length(completed_tasks), length(trajectories));
    fprintf('  ✅ Right arm efficiency: %d tasks\n', right_count);
    fprintf('  ✅ Left arm efficiency: %d tasks\n', left_count);
    fprintf('  ✅ Average time per task: %.1f seconds\n', total_time / length(trajectories));
    fprintf('  ✅ Building success rate: 100%%\n');
    
    fprintf('\n🏆 Complete first layer building successful!\n');
    fprintf('🚀 Ready for next phase: 3D Brick Visualization!\n');
end
