#!/usr/bin/env python3
"""
LEGO城堡搭建系统测试脚本
验证所有模块的功能和集成

作者: AI Assistant
日期: 2025-01-26
版本: 1.0
"""

import sys
import os
import unittest
import logging
import time
import json
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入测试模块
from castle_structure import CastleStructureDefinition, BrickType, BrickOrientation
from yumi_controller import YuMiDualArmController, CartesianPose
from physics_simulation import PhysicsSimulator, PhysicsProperties
from collision_detection import CollisionDetector, AABB, OBB
from stability_analyzer import StabilityAnalyzer, StabilityRating
from visualization import CastleVisualizer
from lego_castle_builder import LegoCastleBuilder, BuildConfig

class TestCastleStructure(unittest.TestCase):
    """测试城堡结构定义"""
    
    def setUp(self):
        self.castle = CastleStructureDefinition()
    
    def test_structure_creation(self):
        """测试结构创建"""
        self.assertIsNotNone(self.castle)
        self.assertGreater(self.castle.get_total_brick_count(), 0)
        self.assertEqual(self.castle.get_level_count(), 8)
    
    def test_structure_validation(self):
        """测试结构验证"""
        self.assertTrue(self.castle.validate_structure())
    
    def test_level_bricks(self):
        """测试各层积木"""
        for level in range(1, 9):
            level_bricks = self.castle.get_level_bricks(level)
            self.assertIsInstance(level_bricks, list)
            self.assertGreater(len(level_bricks), 0)
            
            # 验证积木属性
            for brick in level_bricks:
                self.assertIn('id', brick)
                self.assertIn('type', brick)
                self.assertIn('position', brick)
                self.assertIn('level', brick)
                self.assertEqual(brick['level'], level)
    
    def test_brick_dependencies(self):
        """测试积木依赖关系"""
        all_ids = set()
        for level in range(1, 9):
            level_bricks = self.castle.get_level_bricks(level)
            for brick in level_bricks:
                all_ids.add(brick['id'])
        
        # 检查依赖关系的有效性
        for level in range(1, 9):
            level_bricks = self.castle.get_level_bricks(level)
            for brick in level_bricks:
                for dep_id in brick.get('dependencies', []):
                    self.assertIn(dep_id, all_ids, f"依赖 {dep_id} 不存在")

class TestYuMiController(unittest.TestCase):
    """测试YuMi控制器"""
    
    def setUp(self):
        self.controller = YuMiDualArmController(
            ip_address="127.0.0.1",  # 使用本地地址进行测试
            left_enabled=True,
            right_enabled=True
        )
    
    def test_controller_creation(self):
        """测试控制器创建"""
        self.assertIsNotNone(self.controller)
        self.assertTrue(self.controller.is_connected())
    
    def test_pose_operations(self):
        """测试位姿操作"""
        test_pose = CartesianPose(x=0.5, y=0.0, z=0.1, rx=180, ry=0, rz=0)
        
        # 测试位姿转换
        pose_list = test_pose.to_list()
        self.assertEqual(len(pose_list), 6)
        self.assertEqual(pose_list[0], 0.5)
    
    def test_gripper_operations(self):
        """测试夹爪操作"""
        # 测试打开夹爪
        self.assertTrue(self.controller.open_gripper("left"))
        self.assertTrue(self.controller.open_gripper("right"))
        
        # 测试关闭夹爪
        self.assertTrue(self.controller.close_gripper("left"))
        self.assertTrue(self.controller.close_gripper("right"))
    
    def test_workspace_limits(self):
        """测试工作空间限制"""
        # 有效位置
        valid_pose = CartesianPose(x=0.5, y=0.0, z=0.1)
        self.assertTrue(self.controller._check_workspace_limits(valid_pose))
        
        # 无效位置
        invalid_pose = CartesianPose(x=1.0, y=0.0, z=0.1)  # 超出X范围
        self.assertFalse(self.controller._check_workspace_limits(invalid_pose))
    
    def tearDown(self):
        self.controller.disconnect()

class TestPhysicsSimulation(unittest.TestCase):
    """测试物理仿真"""
    
    def setUp(self):
        self.simulator = PhysicsSimulator(gui=False)
    
    def test_simulator_creation(self):
        """测试仿真器创建"""
        self.assertIsNotNone(self.simulator)
        self.assertIsNotNone(self.simulator.physics_client)
    
    def test_brick_creation(self):
        """测试积木创建"""
        brick_id = self.simulator.create_brick(
            "test_brick", [0, 0, 0.1], 0, "brick_2x4"
        )
        self.assertGreater(brick_id, 0)
        self.assertIn("test_brick", self.simulator.objects)
    
    def test_physics_properties(self):
        """测试物理属性"""
        props = PhysicsProperties()
        
        # 测试质量计算
        mass = props.get_mass()
        self.assertAlmostEqual(mass, 0.00253, places=5)
        
        # 测试惯性计算
        inertia = props.get_inertia()
        self.assertEqual(len(inertia), 3)
        self.assertGreater(inertia[0], 0)
    
    def test_energy_calculation(self):
        """测试能量计算"""
        # 创建测试积木
        self.simulator.create_brick("brick1", [0, 0, 0.1], 0, "brick_2x4")
        
        # 计算能量
        energy = self.simulator.calculate_system_energy()
        
        self.assertIn('kinetic_energy', energy)
        self.assertIn('potential_energy', energy)
        self.assertIn('total_energy', energy)
        self.assertGreater(energy['potential_energy'], 0)
    
    def tearDown(self):
        self.simulator.cleanup()

class TestCollisionDetection(unittest.TestCase):
    """测试碰撞检测"""
    
    def setUp(self):
        self.detector = CollisionDetector(tolerance=1e-4)
    
    def test_detector_creation(self):
        """测试检测器创建"""
        self.assertIsNotNone(self.detector)
        self.assertEqual(self.detector.tolerance, 1e-4)
    
    def test_aabb_creation(self):
        """测试AABB创建"""
        import numpy as np
        position = np.array([0, 0, 0])
        size = np.array([0.1, 0.1, 0.1])
        
        aabb = self.detector.create_aabb_from_brick(position, size)
        
        self.assertIsInstance(aabb, AABB)
        np.testing.assert_array_equal(aabb.get_center(), position)
    
    def test_aabb_overlap(self):
        """测试AABB重叠检测"""
        import numpy as np
        
        # 创建两个重叠的AABB
        aabb1 = AABB(np.array([0, 0, 0]), np.array([1, 1, 1]))
        aabb2 = AABB(np.array([0.5, 0.5, 0.5]), np.array([1.5, 1.5, 1.5]))
        
        self.assertTrue(aabb1.overlaps(aabb2))
        
        # 创建两个不重叠的AABB
        aabb3 = AABB(np.array([2, 2, 2]), np.array([3, 3, 3]))
        self.assertFalse(aabb1.overlaps(aabb3))
    
    def test_collision_detection(self):
        """测试碰撞检测"""
        # 创建测试对象
        objects = {
            'brick1': {
                'position': [0, 0, 0],
                'orientation': 0,
                'size': [0.1, 0.1, 0.1]
            },
            'brick2': {
                'position': [0.05, 0, 0],  # 部分重叠
                'orientation': 0,
                'size': [0.1, 0.1, 0.1]
            }
        }
        
        collisions = self.detector.check_collisions(objects)
        self.assertGreater(len(collisions), 0)

class TestStabilityAnalyzer(unittest.TestCase):
    """测试稳定性分析"""
    
    def setUp(self):
        self.analyzer = StabilityAnalyzer(threshold=0.7)
    
    def test_analyzer_creation(self):
        """测试分析器创建"""
        self.assertIsNotNone(self.analyzer)
        self.assertEqual(self.analyzer.threshold, 0.7)
    
    def test_center_of_mass_analysis(self):
        """测试重心分析"""
        objects = {
            'brick1': {
                'position': [0, 0, 0.1],
                'mass': 0.00253,
                'size': [0.0318, 0.0159, 0.0096]
            },
            'brick2': {
                'position': [0.032, 0, 0.1],
                'mass': 0.00253,
                'size': [0.0318, 0.0159, 0.0096]
            }
        }
        
        com_analysis = self.analyzer.analyze_center_of_mass(objects)
        
        self.assertIsNotNone(com_analysis.system_com)
        self.assertEqual(len(com_analysis.system_com), 3)
        self.assertGreater(com_analysis.stability_score, 0)
    
    def test_support_analysis(self):
        """测试支撑分析"""
        objects = {
            'brick1': {
                'position': [0, 0, 0.065],
                'level': 1,
                'size': [0.0318, 0.0159, 0.0096]
            },
            'brick2': {
                'position': [0, 0, 0.075],
                'level': 2,
                'size': [0.0318, 0.0159, 0.0096]
            }
        }
        
        support_analysis = self.analyzer.analyze_support_structure(objects)
        
        self.assertEqual(support_analysis.num_layers, 2)
        self.assertEqual(len(support_analysis.layer_support_ratios), 2)
        self.assertIsInstance(support_analysis.support_rating, StabilityRating)
    
    def test_complete_stability_analysis(self):
        """测试完整稳定性分析"""
        objects = {
            'brick1': {
                'position': [0, 0, 0.065],
                'level': 1,
                'mass': 0.00253,
                'size': [0.0318, 0.0159, 0.0096]
            }
        }
        
        report = self.analyzer.analyze_complete_stability(objects)
        
        self.assertIsNotNone(report)
        self.assertGreater(report.overall_score, 0)
        self.assertIsInstance(report.overall_rating, StabilityRating)
        self.assertIsInstance(report.is_stable, bool)

class TestVisualization(unittest.TestCase):
    """测试可视化"""
    
    def setUp(self):
        self.visualizer = CastleVisualizer(real_time=False)
    
    def test_visualizer_creation(self):
        """测试可视化器创建"""
        self.assertIsNotNone(self.visualizer)
        self.assertIn(self.visualizer.visualization_engine, ['open3d', 'matplotlib'])
    
    def test_brick_addition(self):
        """测试积木添加"""
        self.visualizer.add_brick("test_brick", [0.5, 0, 0.065], 0, "brick_2x4", "tan")
        self.assertIn("test_brick", self.visualizer.objects)
    
    def test_progress_update(self):
        """测试进度更新"""
        self.visualizer.update_progress(level=3, completed=10, total=43)
        self.assertEqual(self.visualizer.build_progress['level'], 3)
        self.assertEqual(self.visualizer.build_progress['completed'], 10)
        self.assertEqual(self.visualizer.build_progress['total'], 43)
    
    def tearDown(self):
        self.visualizer.close()

class TestSystemIntegration(unittest.TestCase):
    """测试系统集成"""
    
    def test_build_config(self):
        """测试构建配置"""
        config = BuildConfig()
        self.assertIsNotNone(config)
        self.assertIsInstance(config.yumi_ip, str)
        self.assertIsInstance(config.left_arm_enabled, bool)
    
    def test_castle_builder_creation(self):
        """测试城堡搭建器创建"""
        config = BuildConfig(
            yumi_ip="127.0.0.1",
            enable_visualization=False  # 禁用可视化以加快测试
        )
        
        try:
            builder = LegoCastleBuilder(config)
            self.assertIsNotNone(builder)
            self.assertEqual(builder.status.value, "idle")
            
            # 测试状态获取
            status = builder.get_status()
            self.assertIn('status', status)
            self.assertIn('progress', status)
            
        except Exception as e:
            # 如果YuMi连接失败，这是预期的（在测试环境中）
            self.assertIn("连接", str(e).lower())
        
        finally:
            if 'builder' in locals():
                builder.cleanup()

def run_performance_tests():
    """运行性能测试"""
    print("\n🚀 运行性能测试...")
    
    # 碰撞检测性能测试
    print("测试碰撞检测性能...")
    detector = CollisionDetector()
    
    # 创建大量测试对象
    objects = {}
    for i in range(100):
        objects[f'brick_{i}'] = {
            'position': [i * 0.01, 0, 0],
            'orientation': 0,
            'size': [0.0318, 0.0159, 0.0096]
        }
    
    start_time = time.time()
    collisions = detector.check_collisions(objects)
    detection_time = time.time() - start_time
    
    print(f"  100个对象碰撞检测时间: {detection_time:.4f}s")
    print(f"  检测到碰撞: {len(collisions)}个")
    
    # 物理仿真性能测试
    print("测试物理仿真性能...")
    simulator = PhysicsSimulator(gui=False)
    
    # 创建测试积木
    for i in range(10):
        simulator.create_brick(f'brick_{i}', [i * 0.05, 0, 0.1], 0, "brick_2x4")
    
    start_time = time.time()
    simulator.run_simulation(duration=1.0, real_time=False)
    simulation_time = time.time() - start_time
    
    print(f"  1秒物理仿真时间: {simulation_time:.4f}s")
    
    simulator.cleanup()

def main():
    """主测试函数"""
    print("🧪 LEGO城堡搭建系统测试")
    print("=" * 50)
    
    # 设置日志级别
    logging.basicConfig(level=logging.WARNING)
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试用例
    test_classes = [
        TestCastleStructure,
        TestYuMiController,
        TestPhysicsSimulation,
        TestCollisionDetection,
        TestStabilityAnalyzer,
        TestVisualization,
        TestSystemIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 运行性能测试
    if result.wasSuccessful():
        run_performance_tests()
    
    # 输出结果
    print("\n" + "=" * 50)
    if result.wasSuccessful():
        print("✅ 所有测试通过！")
        print(f"运行了 {result.testsRun} 个测试")
    else:
        print("❌ 部分测试失败")
        print(f"失败: {len(result.failures)}, 错误: {len(result.errors)}")
        
        # 显示失败详情
        for test, traceback in result.failures + result.errors:
            print(f"\n失败测试: {test}")
            print(f"错误信息: {traceback}")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
