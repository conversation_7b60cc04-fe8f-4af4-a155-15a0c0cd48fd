%% Regenerate YuMi Model for Current MATLAB Version
% This script regenerates the YuMi Simulink model to be compatible with current MATLAB version

clc; clear; close all;
fprintf('=== Regenerating YuMi Model for Compatibility ===\n\n');

%% 1. Check MATLAB version
matlab_version = version;
fprintf('Current MATLAB Version: %s\n', matlab_version);

%% 2. Load YuMi robot model
fprintf('\n2. Loading YuMi robot model...\n');
try
    yumi = loadrobot('abbYumi', 'DataFormat', 'row', 'Gravity', [0 0 -9.81]);
    qHome = yumi.homeConfiguration;
    fprintf('   ✓ YuMi robot model loaded successfully\n');
    fprintf('   ✓ Joint count: %d\n', length(qHome));
    fprintf('   ✓ Home configuration set\n');
catch ME
    fprintf('   ❌ Failed to load YuMi robot: %s\n', ME.message);
    return;
end

%% 3. Export to URDF
fprintf('\n3. Exporting YuMi to URDF...\n');
try
    urdf_filename = 'yumi_exported_compatible.urdf';
    exportrobot(yumi, urdf_filename);
    fprintf('   ✓ URDF exported to: %s\n', urdf_filename);
catch ME
    fprintf('   ❌ URDF export failed: %s\n', ME.message);
    return;
end

%% 4. Import to Simscape Multibody
fprintf('\n4. Importing to Simscape Multibody...\n');
try
    % Check if Simscape Multibody is available
    if license('test', 'Simscape_Multibody')
        fprintf('   ✓ Simscape Multibody license available\n');
        
        % Import URDF to Simscape
        model_name = 'YumiSimscape_Compatible';
        smimport(urdf_filename, 'ModelName', model_name);
        fprintf('   ✓ Simscape model created: %s.slx\n', model_name);
        
        % Save the model
        save_system(model_name);
        fprintf('   ✓ Model saved successfully\n');
        
    else
        fprintf('   ❌ Simscape Multibody license not available\n');
        fprintf('   💡 Alternative: Use existing model or request license\n');
        
        % Try alternative approach - copy existing model
        try_alternative_approach();
    end
    
catch ME
    fprintf('   ❌ Simscape import failed: %s\n', ME.message);
    fprintf('   💡 Trying alternative approach...\n');
    try_alternative_approach();
end

%% 5. Test the new model
fprintf('\n5. Testing the new model...\n');
try
    if exist('YumiSimscape_Compatible.slx', 'file')
        load_system('YumiSimscape_Compatible.slx');
        fprintf('   ✓ New model loads successfully\n');
        
        % Update setupRobotEnv to use new model
        update_setup_script();
        
    else
        fprintf('   ⚠️  New model not found, using alternative approach\n');
    end
catch ME
    fprintf('   ❌ Model test failed: %s\n', ME.message);
end

fprintf('\n=== Model Regeneration Complete ===\n');

function try_alternative_approach()
    fprintf('\n--- Alternative Approach ---\n');
    fprintf('Since Simscape Multibody is not available, we will:\n');
    fprintf('1. Use the existing model structure\n');
    fprintf('2. Focus on MATLAB-based trajectory planning\n');
    fprintf('3. Create a simplified simulation approach\n');
    
    % Create a simplified test environment
    create_simplified_test();
end

function update_setup_script()
    fprintf('   📝 Updating setupRobotEnv.m to use compatible model...\n');
    
    % Read current setupRobotEnv.m
    if exist('setupRobotEnv.m', 'file')
        % Create backup
        copyfile('setupRobotEnv.m', 'setupRobotEnv_backup.m');
        
        % Update to use compatible model
        content = fileread('setupRobotEnv.m');
        updated_content = strrep(content, 'YumiSimscape', 'YumiSimscape_Compatible');
        
        % Write updated content
        fid = fopen('setupRobotEnv_compatible.m', 'w');
        fprintf(fid, '%s', updated_content);
        fclose(fid);
        
        fprintf('   ✓ Created setupRobotEnv_compatible.m\n');
    end
end

function create_simplified_test()
    fprintf('   🔧 Creating simplified test environment...\n');
    
    % Create a MATLAB-only test script
    test_script = [
        '%% Simplified YuMi LEGO Test (MATLAB Only)\n'
        'clc; clear; close all;\n'
        'fprintf(''=== Simplified YuMi LEGO Test ===\\n'');\n'
        '\n'
        '%% 1. Load robot model\n'
        'yumi = loadrobot(''abbYumi'', ''DataFormat'', ''row'');\n'
        'qHome = yumi.homeConfiguration;\n'
        'fprintf(''✓ YuMi model loaded\\n'');\n'
        '\n'
        '%% 2. Load LEGO configuration\n'
        'brick_config = lego_config();\n'
        'fprintf(''✓ LEGO config loaded\\n'');\n'
        '\n'
        '%% 3. Test trajectory planning\n'
        'test_config = brick_config;\n'
        'test_config.task_sequence = brick_config.task_sequence(1);\n'
        'trajectories = planTrajectory(yumi, test_config, qHome);\n'
        '\n'
        'if ~isempty(trajectories)\n'
        '    fprintf(''✓ Trajectory planning successful\\n'');\n'
        '    fprintf(''Generated %d trajectories\\n'', length(trajectories));\n'
        'else\n'
        '    fprintf(''❌ Trajectory planning failed\\n'');\n'
        'end\n'
        '\n'
        'fprintf(''\\nSimplified test complete!\\n'');\n'
    ];
    
    % Write simplified test script
    fid = fopen('simplified_test.m', 'w');
    fprintf(fid, '%s', test_script);
    fclose(fid);
    
    fprintf('   ✓ Created simplified_test.m\n');
    fprintf('   💡 Run this script to test basic functionality\n');
end
