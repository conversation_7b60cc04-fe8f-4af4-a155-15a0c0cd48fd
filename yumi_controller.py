#!/usr/bin/env python3
"""
YuMi双臂机械臂控制模块
实现YuMi机械臂的精确控制，包括双臂协调、避障、夹爪控制等功能

作者: AI Assistant
日期: 2025-01-26
版本: 1.0
"""

import numpy as np
import time
import threading
import logging
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import socket
import json

# 模拟YuMi SDK导入 (实际使用时需要安装ABB RobotStudio SDK)
try:
    # import abb_robot_sdk  # 实际的ABB SDK
    pass
except ImportError:
    print("警告: ABB Robot SDK未安装，使用模拟模式")

class ArmSide(Enum):
    """机械臂侧别枚举"""
    LEFT = "left"
    RIGHT = "right"
    BOTH = "both"

class GripperState(Enum):
    """夹爪状态枚举"""
    OPEN = "open"
    CLOSED = "closed"
    GRIPPING = "gripping"

class MotionType(Enum):
    """运动类型枚举"""
    LINEAR = "linear"      # 直线运动
    JOINT = "joint"        # 关节运动
    CIRCULAR = "circular"  # 圆弧运动

@dataclass
class JointAngles:
    """关节角度数据类"""
    j1: float = 0.0
    j2: float = 0.0
    j3: float = 0.0
    j4: float = 0.0
    j5: float = 0.0
    j6: float = 0.0
    j7: float = 0.0
    
    def to_list(self) -> List[float]:
        return [self.j1, self.j2, self.j3, self.j4, self.j5, self.j6, self.j7]

@dataclass
class CartesianPose:
    """笛卡尔位姿数据类"""
    x: float = 0.0
    y: float = 0.0
    z: float = 0.0
    rx: float = 0.0  # 绕X轴旋转
    ry: float = 0.0  # 绕Y轴旋转
    rz: float = 0.0  # 绕Z轴旋转
    
    def to_list(self) -> List[float]:
        return [self.x, self.y, self.z, self.rx, self.ry, self.rz]

class YuMiDualArmController:
    """YuMi双臂机械臂控制器"""
    
    def __init__(self, ip_address: str = "*************", 
                 left_enabled: bool = True, right_enabled: bool = True):
        """初始化YuMi控制器"""
        self.ip_address = ip_address
        self.left_enabled = left_enabled
        self.right_enabled = right_enabled
        self.connected = False
        
        # 设置日志
        self.logger = logging.getLogger('YuMiController')
        
        # 机械臂状态
        self.left_arm_busy = False
        self.right_arm_busy = False
        self.emergency_stop_active = False
        
        # 当前位姿
        self.left_current_pose = CartesianPose()
        self.right_current_pose = CartesianPose()
        
        # 夹爪状态
        self.left_gripper_state = GripperState.OPEN
        self.right_gripper_state = GripperState.OPEN
        
        # 运动参数
        self.default_speed = 50  # mm/s
        self.default_acceleration = 100  # mm/s²
        
        # 工作空间限制
        self.workspace_limits = {
            'x_min': 0.2, 'x_max': 0.8,
            'y_min': -0.4, 'y_max': 0.4,
            'z_min': 0.05, 'z_max': 0.3
        }
        
        # 安全距离
        self.collision_threshold = 0.05  # 5cm安全距离
        
        # 初始化连接
        self._initialize_connection()
    
    def _initialize_connection(self):
        """初始化与YuMi的连接"""
        try:
            self.logger.info(f"连接YuMi机械臂: {self.ip_address}")
            
            # 模拟连接过程
            time.sleep(1)  # 模拟连接延迟
            
            # 实际实现中应该使用ABB SDK
            # self.robot = abb_robot_sdk.Robot(self.ip_address)
            # self.robot.connect()
            
            self.connected = True
            self.logger.info("✅ YuMi连接成功")
            
            # 初始化机械臂到Home位置
            self._move_to_home_position()
            
        except Exception as e:
            self.logger.error(f"❌ YuMi连接失败: {e}")
            self.connected = False
            raise
    
    def _move_to_home_position(self):
        """移动到Home位置"""
        self.logger.info("移动到Home位置...")
        
        # 定义Home位置
        left_home = CartesianPose(x=0.3, y=0.2, z=0.15, rx=180, ry=0, rz=0)
        right_home = CartesianPose(x=0.3, y=-0.2, z=0.15, rx=180, ry=0, rz=0)
        
        # 同时移动双臂到Home位置
        if self.left_enabled:
            self._move_arm_to_pose("left", left_home, speed=30)
        
        if self.right_enabled:
            self._move_arm_to_pose("right", right_home, speed=30)
        
        # 打开夹爪
        self.open_gripper("both")
        
        self.logger.info("✅ 已移动到Home位置")
    
    def is_connected(self) -> bool:
        """检查连接状态"""
        return self.connected and not self.emergency_stop_active
    
    def pick_brick(self, arm: str, position: List[float], brick_type: str) -> bool:
        """拾取积木"""
        self.logger.info(f"使用{arm}臂拾取积木: {brick_type} at {position}")
        
        if not self._validate_arm_operation(arm):
            return False
        
        try:
            # 1. 移动到积木上方
            approach_pose = CartesianPose(
                x=position[0], y=position[1], z=position[2] + 0.05,
                rx=180, ry=0, rz=0
            )
            
            if not self._move_arm_to_pose(arm, approach_pose):
                return False
            
            # 2. 打开夹爪
            self.open_gripper(arm)
            time.sleep(0.5)
            
            # 3. 下降到积木位置
            pick_pose = CartesianPose(
                x=position[0], y=position[1], z=position[2],
                rx=180, ry=0, rz=0
            )
            
            if not self._move_arm_to_pose(arm, pick_pose, speed=20):
                return False
            
            # 4. 夹取积木
            if not self.close_gripper(arm):
                return False
            
            # 5. 提升积木
            lift_pose = CartesianPose(
                x=position[0], y=position[1], z=position[2] + 0.03,
                rx=180, ry=0, rz=0
            )
            
            if not self._move_arm_to_pose(arm, lift_pose, speed=20):
                return False
            
            self.logger.info(f"✅ {arm}臂成功拾取积木")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ {arm}臂拾取积木失败: {e}")
            return False
    
    def place_brick(self, arm: str, position: List[float], orientation: float,
                   approach_height: float = 0.05, speed: float = 0.05) -> bool:
        """放置积木"""
        self.logger.info(f"使用{arm}臂放置积木: {position}, 朝向: {orientation}°")
        
        if not self._validate_arm_operation(arm):
            return False
        
        try:
            # 1. 移动到目标位置上方
            approach_pose = CartesianPose(
                x=position[0], y=position[1], z=position[2] + approach_height,
                rx=180, ry=0, rz=orientation
            )
            
            if not self._move_arm_to_pose(arm, approach_pose):
                return False
            
            # 2. 缓慢下降到目标位置
            place_pose = CartesianPose(
                x=position[0], y=position[1], z=position[2],
                rx=180, ry=0, rz=orientation
            )
            
            if not self._move_arm_to_pose(arm, place_pose, speed=speed*1000):  # 转换为mm/s
                return False
            
            # 3. 释放积木
            time.sleep(0.2)  # 稳定时间
            
            if not self.open_gripper(arm):
                return False
            
            # 4. 缓慢提升
            retreat_pose = CartesianPose(
                x=position[0], y=position[1], z=position[2] + 0.02,
                rx=180, ry=0, rz=orientation
            )
            
            if not self._move_arm_to_pose(arm, retreat_pose, speed=20):
                return False
            
            self.logger.info(f"✅ {arm}臂成功放置积木")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ {arm}臂放置积木失败: {e}")
            return False
    
    def _move_arm_to_pose(self, arm: str, target_pose: CartesianPose, 
                         speed: float = None, motion_type: MotionType = MotionType.LINEAR) -> bool:
        """移动机械臂到指定位姿"""
        if speed is None:
            speed = self.default_speed
        
        # 检查工作空间限制
        if not self._check_workspace_limits(target_pose):
            self.logger.warning(f"目标位置超出工作空间: {target_pose}")
            return False
        
        # 检查碰撞
        if not self._check_collision_avoidance(arm, target_pose):
            self.logger.warning(f"检测到潜在碰撞风险")
            return False
        
        try:
            # 设置机械臂忙碌状态
            if arm == "left":
                self.left_arm_busy = True
            else:
                self.right_arm_busy = True
            
            # 模拟运动执行
            self.logger.debug(f"移动{arm}臂到: {target_pose.to_list()}")
            
            # 实际实现中应该调用ABB SDK
            # if arm == "left":
            #     self.robot.left_arm.move_to_pose(target_pose.to_list(), speed)
            # else:
            #     self.robot.right_arm.move_to_pose(target_pose.to_list(), speed)
            
            # 模拟运动时间
            distance = self._calculate_movement_distance(arm, target_pose)
            movement_time = distance / (speed / 1000)  # 转换为秒
            time.sleep(min(movement_time, 3.0))  # 最大3秒
            
            # 更新当前位姿
            if arm == "left":
                self.left_current_pose = target_pose
                self.left_arm_busy = False
            else:
                self.right_current_pose = target_pose
                self.right_arm_busy = False
            
            return True
            
        except Exception as e:
            self.logger.error(f"移动{arm}臂失败: {e}")
            if arm == "left":
                self.left_arm_busy = False
            else:
                self.right_arm_busy = False
            return False
    
    def open_gripper(self, arm: str) -> bool:
        """打开夹爪"""
        try:
            if arm == "left" or arm == "both":
                # 实际实现: self.robot.left_gripper.open()
                self.left_gripper_state = GripperState.OPEN
                self.logger.debug("左夹爪已打开")
            
            if arm == "right" or arm == "both":
                # 实际实现: self.robot.right_gripper.open()
                self.right_gripper_state = GripperState.OPEN
                self.logger.debug("右夹爪已打开")
            
            time.sleep(0.5)  # 夹爪动作时间
            return True
            
        except Exception as e:
            self.logger.error(f"打开夹爪失败: {e}")
            return False
    
    def close_gripper(self, arm: str, force: float = 20.0) -> bool:
        """关闭夹爪"""
        try:
            if arm == "left":
                # 实际实现: self.robot.left_gripper.close(force)
                self.left_gripper_state = GripperState.CLOSED
                self.logger.debug("左夹爪已关闭")
            
            elif arm == "right":
                # 实际实现: self.robot.right_gripper.close(force)
                self.right_gripper_state = GripperState.CLOSED
                self.logger.debug("右夹爪已关闭")
            
            time.sleep(0.5)  # 夹爪动作时间
            
            # 检查是否成功夹取
            if self._check_gripper_grasp(arm):
                if arm == "left":
                    self.left_gripper_state = GripperState.GRIPPING
                else:
                    self.right_gripper_state = GripperState.GRIPPING
                return True
            else:
                self.logger.warning(f"{arm}夹爪未能成功夹取物体")
                return False
            
        except Exception as e:
            self.logger.error(f"关闭夹爪失败: {e}")
            return False
    
    def _check_gripper_grasp(self, arm: str) -> bool:
        """检查夹爪是否成功夹取"""
        # 实际实现中应该检查夹爪传感器
        # 这里简化为随机成功率
        import random
        return random.random() > 0.1  # 90%成功率
    
    def _validate_arm_operation(self, arm: str) -> bool:
        """验证机械臂操作的有效性"""
        if not self.is_connected():
            self.logger.error("YuMi未连接或处于紧急停止状态")
            return False
        
        if arm == "left" and not self.left_enabled:
            self.logger.error("左臂未启用")
            return False
        
        if arm == "right" and not self.right_enabled:
            self.logger.error("右臂未启用")
            return False
        
        if arm == "left" and self.left_arm_busy:
            self.logger.warning("左臂正忙")
            return False
        
        if arm == "right" and self.right_arm_busy:
            self.logger.warning("右臂正忙")
            return False
        
        return True
    
    def _check_workspace_limits(self, pose: CartesianPose) -> bool:
        """检查位置是否在工作空间内"""
        return (self.workspace_limits['x_min'] <= pose.x <= self.workspace_limits['x_max'] and
                self.workspace_limits['y_min'] <= pose.y <= self.workspace_limits['y_max'] and
                self.workspace_limits['z_min'] <= pose.z <= self.workspace_limits['z_max'])
    
    def _check_collision_avoidance(self, arm: str, target_pose: CartesianPose) -> bool:
        """检查碰撞避障"""
        # 检查与另一只机械臂的距离
        if arm == "left":
            other_pose = self.right_current_pose
        else:
            other_pose = self.left_current_pose
        
        distance = np.sqrt((target_pose.x - other_pose.x)**2 + 
                          (target_pose.y - other_pose.y)**2 + 
                          (target_pose.z - other_pose.z)**2)
        
        return distance > self.collision_threshold
    
    def _calculate_movement_distance(self, arm: str, target_pose: CartesianPose) -> float:
        """计算运动距离"""
        if arm == "left":
            current = self.left_current_pose
        else:
            current = self.right_current_pose
        
        return np.sqrt((target_pose.x - current.x)**2 + 
                      (target_pose.y - current.y)**2 + 
                      (target_pose.z - current.z)**2)
    
    def get_current_pose(self, arm: str) -> CartesianPose:
        """获取当前位姿"""
        if arm == "left":
            return self.left_current_pose
        else:
            return self.right_current_pose
    
    def get_gripper_state(self, arm: str) -> GripperState:
        """获取夹爪状态"""
        if arm == "left":
            return self.left_gripper_state
        else:
            return self.right_gripper_state
    
    def emergency_stop(self):
        """紧急停止"""
        self.logger.warning("⚠️ 执行紧急停止")
        self.emergency_stop_active = True
        
        # 实际实现中应该调用机械臂的紧急停止
        # self.robot.emergency_stop()
        
        # 重置状态
        self.left_arm_busy = False
        self.right_arm_busy = False
    
    def reset_emergency_stop(self):
        """重置紧急停止"""
        self.logger.info("重置紧急停止状态")
        self.emergency_stop_active = False
        
        # 实际实现中应该重置机械臂状态
        # self.robot.reset_emergency_stop()
    
    def get_status(self) -> Dict:
        """获取系统状态"""
        return {
            'connected': self.connected,
            'emergency_stop': self.emergency_stop_active,
            'left_arm': {
                'enabled': self.left_enabled,
                'busy': self.left_arm_busy,
                'pose': self.left_current_pose.to_list(),
                'gripper': self.left_gripper_state.value
            },
            'right_arm': {
                'enabled': self.right_enabled,
                'busy': self.right_arm_busy,
                'pose': self.right_current_pose.to_list(),
                'gripper': self.right_gripper_state.value
            }
        }
    
    def disconnect(self):
        """断开连接"""
        self.logger.info("断开YuMi连接")
        
        # 移动到安全位置
        if self.connected:
            self._move_to_home_position()
        
        # 实际实现中应该断开机械臂连接
        # self.robot.disconnect()
        
        self.connected = False
        self.logger.info("✅ YuMi已断开连接")

def main():
    """测试YuMi控制器"""
    print("🤖 YuMi双臂机械臂控制器测试")
    print("=" * 40)
    
    # 创建控制器
    controller = YuMiDualArmController(
        ip_address="*************",
        left_enabled=True,
        right_enabled=True
    )
    
    try:
        # 测试基本功能
        print("测试连接状态:", controller.is_connected())
        print("系统状态:", json.dumps(controller.get_status(), indent=2))
        
        # 测试拾取和放置
        test_position = [0.4, 0.1, 0.08]
        
        print(f"\n测试左臂拾取积木...")
        success = controller.pick_brick("left", test_position, "brick_2x4")
        print(f"拾取结果: {'成功' if success else '失败'}")
        
        if success:
            place_position = [0.5, 0.0, 0.08]
            print(f"\n测试左臂放置积木...")
            success = controller.place_brick("left", place_position, 0)
            print(f"放置结果: {'成功' if success else '失败'}")
        
        print("\n✅ YuMi控制器测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    finally:
        controller.disconnect()

if __name__ == "__main__":
    main()
