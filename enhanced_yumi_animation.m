function [sim_results] = enhanced_yumi_animation()
    % 增强版 YuMi LEGO 城堡堆叠动画仿真
    % 包含完整的动画录制和视频输出功能
    
    fprintf('=== 增强版 YuMi LEGO 城堡堆叠动画仿真 ===\n\n');
    
    %% 1. 初始化参数
    fprintf('1. 初始化动画参数...\n');
    
    % 动画参数
    dt = 0.05;  % 时间步长 (50ms)
    t_total = 60;  % 总仿真时间 (60秒)
    time_steps = 0:dt:t_total;
    n_steps = length(time_steps);
    
    % 视频参数
    frame_rate = 20;  % 帧率
    video_filename = 'enhanced_yumi_lego_animation.mp4';
    
    % 坐标系旋转矩阵（90度修正）
    coord_rotation = [0 1 0; -1 0 0; 0 0 1];
    
    fprintf('✓ 动画参数设置完成\n');
    fprintf('  - 时间步长: %.3f s\n', dt);
    fprintf('  - 总时间: %.1f s\n', t_total);
    fprintf('  - 帧率: %d fps\n', frame_rate);
    fprintf('  - 视频文件: %s\n', video_filename);
    
    %% 2. 加载机器人环境
    fprintf('\n2. 加载机器人环境...\n');
    
    try
        [yumi, qHome, table, ax] = setupRobotEnv_fixed();
        fprintf('✓ 机器人环境加载成功\n');
    catch ME
        fprintf('✗ 机器人环境加载失败: %s\n', ME.message);
        return;
    end
    
    %% 3. 初始化增强版LEGO积木
    fprintf('\n3. 初始化增强版LEGO积木...\n');
    
    lego_blocks = struct();
    lego_blocks.count = 12;
    lego_blocks.size = [0.0636, 0.0318, 0.0400];  % 增大尺寸（2倍）
    lego_blocks.mass = 0.004;
    
    % 颜色配置（红色左臂，蓝色右臂）
    lego_blocks.colors = zeros(12, 3);
    for i = 1:6
        lego_blocks.colors(i, :) = [0.9, 0.1, 0.1];  % 鲜红色
    end
    for i = 7:12
        lego_blocks.colors(i, :) = [0.1, 0.1, 0.9];  % 鲜蓝色
    end
    
    % 初始化数组
    lego_blocks.positions = zeros(lego_blocks.count, 3, n_steps);
    lego_blocks.states = strings(lego_blocks.count, n_steps);
    
    % 设置初始位置（应用坐标系旋转）
    for i = 1:6  % 左臂积木
        pos_original = [-0.15 + (i-1)*0.05, 0.25, 0.07];
        pos_rotated = (coord_rotation * pos_original')';
        lego_blocks.positions(i, :, 1) = pos_rotated;
        lego_blocks.states(i, 1) = "free";
    end
    
    for i = 7:12  % 右臂积木
        pos_original = [-0.15 + (i-7)*0.05, -0.25, 0.07];
        pos_rotated = (coord_rotation * pos_original')';
        lego_blocks.positions(i, :, 1) = pos_rotated;
        lego_blocks.states(i, 1) = "free";
    end
    
    fprintf('✓ 增强版LEGO积木初始化完成\n');
    fprintf('  - 积木数量: %d\n', lego_blocks.count);
    fprintf('  - 增强尺寸: [%.4f, %.4f, %.4f] m\n', lego_blocks.size);
    fprintf('  - 颜色配置: 红色(左臂) + 蓝色(右臂)\n');
    
    %% 4. 定义目标位置
    fprintf('\n4. 定义目标位置...\n');
    
    % 原始目标位置（城堡第一层布局）
    target_positions_original = [
        0.0000, 0.4125, 0.0648;  % B01
        0.0000, 0.5875, 0.0648;  % B02
        -0.0080, 0.4364, 0.0648; % B03
        -0.0080, 0.5636, 0.0648; % B04
        0.0080, 0.4364, 0.0648;  % B05
        0.0080, 0.5636, 0.0648;  % B06
        -0.0080, 0.4682, 0.0648; % B07
        -0.0080, 0.5318, 0.0648; % B08
        0.0080, 0.4682, 0.0648;  % B09
        0.0080, 0.5318, 0.0648;  % B10
        -0.0080, 0.5000, 0.0648; % B11
        0.0080, 0.5000, 0.0648   % B12
    ];
    
    % 应用坐标系旋转
    target_positions = zeros(size(target_positions_original));
    for i = 1:size(target_positions_original, 1)
        target_positions(i, :) = (coord_rotation * target_positions_original(i, :)')';
    end
    
    fprintf('✓ 目标位置定义完成\n');
    
    %% 5. 创建交替任务序列
    fprintf('\n5. 创建交替任务序列...\n');
    
    task_sequence = struct();
    task_sequence.tasks = [];
    
    % 交替执行顺序：左右臂交替
    task_order = [1, 7, 2, 8, 3, 9, 4, 10, 5, 11, 6, 12];
    
    for idx = 1:length(task_order)
        i = task_order(idx);
        task = struct();
        task.block_id = i;
        
        if i <= 6
            task.arm = "left";
        else
            task.arm = "right";
        end
        
        task.pick_pos = lego_blocks.positions(i, :, 1);
        task.place_pos = target_positions(i, :);
        task.status = "pending";
        task.start_time = (idx-1) * 5.0;  % 每个任务5秒
        task.duration = 4.0;  % 4秒完成动作
        task.color = lego_blocks.colors(i, :);
        
        task_sequence.tasks = [task_sequence.tasks; task];
    end
    
    fprintf('✓ 交替任务序列创建完成\n');
    fprintf('  - 任务总数: %d\n', length(task_sequence.tasks));
    fprintf('  - 执行顺序: ');
    for i = 1:length(task_order)
        fprintf('B%02d(%s) ', task_order(i), task_sequence.tasks(i).arm);
    end
    fprintf('\n');
    
    %% 6. 创建动画环境
    fprintf('\n6. 创建动画环境...\n');
    
    % 创建图形窗口
    fig = figure('Name', '增强版 YuMi LEGO 城堡堆叠动画', ...
                 'Position', [100, 100, 1400, 900], ...
                 'Color', 'white');
    clf;
    
    % 重新设置机器人环境
    [yumi, qHome, table, ax] = setupRobotEnv_fixed();
    hold on;
    
    % 设置视角和照明
    view(45, 30);
    axis equal;
    grid on;
    lighting gouraud;
    light('Position', [1, 1, 1]);
    
    % 设置坐标轴范围
    xlim([-0.2, 1.0]);
    ylim([-0.4, 0.4]);
    zlim([0, 0.6]);
    
    % 初始化积木图形句柄
    block_handles = [];
    text_handles = [];
    
    for i = 1:lego_blocks.count
        pos = lego_blocks.positions(i, :, 1);
        
        % 创建增强版积木（使用patch对象获得更好的视觉效果）
        [vertices, faces] = create_lego_block_mesh(lego_blocks.size);
        
        % 平移到正确位置
        vertices = vertices + pos;
        
        h = patch('Vertices', vertices, 'Faces', faces, ...
                  'FaceColor', lego_blocks.colors(i, :), ...
                  'EdgeColor', 'k', 'LineWidth', 1.5, ...
                  'FaceAlpha', 0.9, 'FaceLighting', 'gouraud');
        block_handles = [block_handles; h];
        
        % 添加积木标签
        ht = text(pos(1), pos(2), pos(3) + lego_blocks.size(3)/2 + 0.02, ...
                  sprintf('B%02d', i), 'HorizontalAlignment', 'center', ...
                  'FontSize', 12, 'FontWeight', 'bold', 'Color', 'k');
        text_handles = [text_handles; ht];
    end
    
    % 绘制目标位置标记
    for i = 1:size(target_positions, 1)
        pos = target_positions(i, :);
        plot3(pos(1), pos(2), pos(3), 'go', 'MarkerSize', 10, ...
              'MarkerFaceColor', 'g', 'MarkerEdgeColor', 'k');
        text(pos(1), pos(2), pos(3) + 0.03, sprintf('T%02d', i), ...
             'HorizontalAlignment', 'center', 'FontSize', 10, ...
             'Color', 'g', 'FontWeight', 'bold');
    end
    
    % 添加状态显示
    status_text = text(0.1, 0.3, 0.5, '', 'FontSize', 14, 'FontWeight', 'bold', ...
                      'BackgroundColor', 'white', 'EdgeColor', 'black', ...
                      'Margin', 5);
    
    % 添加进度条背景
    progress_bg = rectangle('Position', [0.1, 0.25, 0.6, 0.03], ...
                           'FaceColor', [0.8, 0.8, 0.8], 'EdgeColor', 'k');
    progress_bar = rectangle('Position', [0.1, 0.25, 0, 0.03], ...
                            'FaceColor', [0.2, 0.8, 0.2], 'EdgeColor', 'none');
    
    title('增强版 YuMi LEGO 城堡堆叠动画仿真', 'FontSize', 16, 'FontWeight', 'bold');
    
    fprintf('✓ 动画环境创建完成\n');
    
    %% 7. 准备视频录制
    fprintf('\n7. 准备视频录制...\n');
    
    % 创建视频写入器
    video_writer = VideoWriter(video_filename, 'MPEG-4');
    video_writer.FrameRate = frame_rate;
    video_writer.Quality = 95;
    open(video_writer);
    
    fprintf('✓ 视频录制准备完成\n');
    
    %% 8. 主动画循环
    fprintf('\n8. 开始动画仿真...\n');
    
    frame_count = 0;
    last_frame_time = 0;
    frame_interval = 1 / frame_rate;
    
    for step = 2:n_steps
        current_time = time_steps(step);
        
        % 更新任务状态
        current_task_info = '';
        active_arm = '';
        
        for i = 1:length(task_sequence.tasks)
            task = task_sequence.tasks(i);
            
            if strcmp(task.status, "pending") && current_time >= task.start_time
                task_sequence.tasks(i).status = "active";
                fprintf('  [%.1fs] 开始任务: %s臂 → 积木B%02d\n', ...
                        current_time, upper(task.arm), task.block_id);
            elseif strcmp(task.status, "active") && current_time >= (task.start_time + task.duration)
                task_sequence.tasks(i).status = "completed";
                fprintf('  [%.1fs] 完成任务: 积木B%02d已放置\n', ...
                        current_time, task.block_id);
            end
            
            if strcmp(task.status, "active")
                current_task_info = sprintf('执行中: %s臂 → 积木B%02d', ...
                                          upper(task.arm), task.block_id);
                active_arm = task.arm;
            end
        end
        
        % 更新积木位置
        for i = 1:lego_blocks.count
            % 复制前一步状态
            lego_blocks.positions(i, :, step) = lego_blocks.positions(i, :, step-1);
            lego_blocks.states(i, step) = lego_blocks.states(i, step-1);
            
            % 检查任务
            task_idx = find([task_sequence.tasks.block_id] == i);
            if ~isempty(task_idx)
                task = task_sequence.tasks(task_idx);
                if strcmp(task.status, "active")
                    % 计算任务进度
                    progress = (current_time - task.start_time) / task.duration;
                    progress = max(0, min(1, progress));
                    
                    % 创建平滑的运动轨迹
                    start_pos = task.pick_pos;
                    end_pos = task.place_pos;
                    
                    % 多阶段运动
                    if progress <= 0.25
                        % 阶段1：移动到拾取位置上方
                        pickup_height = start_pos + [0, 0, 0.15];
                        local_progress = progress / 0.25;
                        current_pos = start_pos + local_progress * (pickup_height - start_pos);
                    elseif progress <= 0.4
                        % 阶段2：下降到拾取位置
                        pickup_height = start_pos + [0, 0, 0.15];
                        local_progress = (progress - 0.25) / 0.15;
                        current_pos = pickup_height + local_progress * (start_pos - pickup_height);
                    elseif progress <= 0.6
                        % 阶段3：提升到运输高度
                        transport_height = start_pos + [0, 0, 0.2];
                        local_progress = (progress - 0.4) / 0.2;
                        current_pos = start_pos + local_progress * (transport_height - start_pos);
                    elseif progress <= 0.85
                        % 阶段4：水平移动到目标上方
                        transport_start = start_pos + [0, 0, 0.2];
                        transport_end = end_pos + [0, 0, 0.2];
                        local_progress = (progress - 0.6) / 0.25;
                        current_pos = transport_start + local_progress * (transport_end - transport_start);
                    else
                        % 阶段5：下降到目标位置
                        target_height = end_pos + [0, 0, 0.2];
                        local_progress = (progress - 0.85) / 0.15;
                        current_pos = target_height + local_progress * (end_pos - target_height);
                    end
                    
                    lego_blocks.positions(i, :, step) = current_pos;
                    lego_blocks.states(i, step) = "moving";
                    
                elseif strcmp(task.status, "completed")
                    lego_blocks.positions(i, :, step) = task.place_pos;
                    lego_blocks.states(i, step) = "placed";
                end
            end
        end
        
        % 动画帧更新
        if current_time - last_frame_time >= frame_interval
            % 更新积木位置
            for i = 1:lego_blocks.count
                pos = lego_blocks.positions(i, :, step);
                
                % 更新积木mesh
                [vertices, faces] = create_lego_block_mesh(lego_blocks.size);
                vertices = vertices + pos;
                
                set(block_handles(i), 'Vertices', vertices);
                
                % 更新标签位置
                set(text_handles(i), 'Position', ...
                    [pos(1), pos(2), pos(3) + lego_blocks.size(3)/2 + 0.02]);
            end
            
            % 更新状态文本
            if ~isempty(current_task_info)
                set(status_text, 'String', ...
                    sprintf('时间: %.1fs | %s', current_time, current_task_info));
            else
                set(status_text, 'String', ...
                    sprintf('时间: %.1fs | 等待下一个任务...', current_time));
            end
            
            % 更新进度条
            completed_count = sum([task_sequence.tasks.status] == "completed");
            progress_ratio = completed_count / length(task_sequence.tasks);
            set(progress_bar, 'Position', [0.1, 0.25, 0.6 * progress_ratio, 0.03]);
            
            % 更新标题
            title(sprintf('增强版 YuMi LEGO 城堡堆叠动画 | 已完成: %d/%d (%.1f%%)', ...
                         completed_count, length(task_sequence.tasks), progress_ratio * 100), ...
                  'FontSize', 16, 'FontWeight', 'bold');
            
            % 刷新显示
            drawnow;
            
            % 录制帧
            frame = getframe(fig);
            writeVideo(video_writer, frame);
            
            frame_count = frame_count + 1;
            last_frame_time = current_time;
        end
        
        % 进度指示
        if mod(step, round(n_steps/20)) == 0
            progress = step / n_steps * 100;
            fprintf('  动画进度: %.1f%% (帧数: %d)\n', progress, frame_count);
        end
    end
    
    % 关闭视频录制
    close(video_writer);
    
    fprintf('✓ 动画录制完成\n');
    fprintf('  - 总帧数: %d\n', frame_count);
    fprintf('  - 视频文件: %s\n', video_filename);
    
    %% 9. 生成结果
    sim_results = struct();
    sim_results.time = time_steps;
    sim_results.lego_blocks = lego_blocks;
    sim_results.task_sequence = task_sequence;
    sim_results.target_positions = target_positions;
    sim_results.coord_rotation = coord_rotation;
    sim_results.animation_info = struct('frame_count', frame_count, ...
                                       'video_file', video_filename, ...
                                       'frame_rate', frame_rate);
    
    % 计算精度
    final_positions = lego_blocks.positions(:, :, end);
    position_errors = zeros(lego_blocks.count, 1);
    for i = 1:lego_blocks.count
        error = norm(final_positions(i, :) - target_positions(i, :));
        position_errors(i) = error;
    end
    
    sim_results.position_errors = position_errors;
    sim_results.mean_error = mean(position_errors);
    sim_results.max_error = max(position_errors);
    
    % 保存结果
    save('enhanced_animation_results.mat', 'sim_results');
    saveas(fig, 'enhanced_animation_final.png');
    
    fprintf('\n=== 增强版动画仿真完成 ===\n');
    fprintf('主要改进:\n');
    fprintf('  ✓ 坐标系修正 (旋转90度)\n');
    fprintf('  ✓ 积木可视化增强 (2倍尺寸 + 鲜明颜色)\n');
    fprintf('  ✓ 动态动画实现 (5阶段运动轨迹)\n');
    fprintf('  ✓ 视频输出功能 (%s)\n', video_filename);
    fprintf('  ✓ 交替执行模式 (左右臂协调)\n');
    fprintf('仿真时间: %.1f 秒\n', t_total);
    fprintf('积木数量: %d\n', lego_blocks.count);
    fprintf('任务完成率: %.1f%%\n', ...
            sum([task_sequence.tasks.status] == "completed") / length(task_sequence.tasks) * 100);
    fprintf('平均精度: %.2f mm\n', sim_results.mean_error * 1000);
    fprintf('动画帧数: %d 帧 @ %d fps\n', frame_count, frame_rate);
    fprintf('状态: 增强版动画仿真成功完成！\n');
    fprintf('=====================================\n');
    
end

function [vertices, faces] = create_lego_block_mesh(size)
    % 创建LEGO积木的3D网格
    % 输入: size - [长度, 宽度, 高度]
    % 输出: vertices - 顶点坐标, faces - 面索引
    
    % 定义长方体的8个顶点
    l = size(1) / 2;
    w = size(2) / 2;
    h = size(3);
    
    vertices = [
        -l, -w, 0;   % 1: 底面左前
         l, -w, 0;   % 2: 底面右前
         l,  w, 0;   % 3: 底面右后
        -l,  w, 0;   % 4: 底面左后
        -l, -w, h;   % 5: 顶面左前
         l, -w, h;   % 6: 顶面右前
         l,  w, h;   % 7: 顶面右后
        -l,  w, h    % 8: 顶面左后
    ];
    
    % 定义6个面（每个面用4个顶点索引表示）
    faces = [
        1, 2, 3, 4;  % 底面
        5, 8, 7, 6;  % 顶面
        1, 5, 6, 2;  % 前面
        3, 7, 8, 4;  % 后面
        1, 4, 8, 5;  % 左面
        2, 6, 7, 3   % 右面
    ];
end
