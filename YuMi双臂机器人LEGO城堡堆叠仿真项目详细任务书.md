# YuMi 双臂机器人 LEGO 城堡堆叠仿真项目详细任务书

## 📋 项目概述与最终目标

### 项目背景
本项目基于现有代码库，旨在通过 MATLAB & Simulink 平台实现 ABB YuMi 双臂协作机器人精确搭建八层 LEGO 城堡模型。项目将在 Simscape Multibody 环境中完整模拟从积木拾取、搬运到最终堆叠的全过程，确保最终成品与 `mainbu.ldr` 设计文件完全一致。

### 核心技术要求
- **参考标准**: 严格遵循 [MathWorks 官方教程](https://uk.mathworks.com/help/robotics/ug/model-and-control-a-manipulator-arm-with-simscape.html)
- **工作区域**: 72×60厘米，分为左手区域、右手区域和中央城堡区域
- **城堡结构**: 8层复杂城堡，第一层12个积木的精确布局
- **技术架构**: Simscape Multibody + 五次多项式轨迹规划 + 精确物理仿真

### 关键成功标准
1. **功能完整性**: Simulink 仿真完整展示从积木拾取到城堡搭建的全流程
2. **动作准确性**: 机器人手臂运动轨迹和夹爪开合时机精确无误
3. **结果一致性**: 最终堆叠结构与 LeoCAD 设计图完全匹配
4. **数据可追溯**: 记录并导出关节角度、末端执行器位姿、接触力等关键数据

---

## 🎯 项目实施路线图

### 阶段一：数据接口优化与坐标系统一 【优先级：最高】

#### 任务 1.1：解决轨迹数据格式问题
**目标**: 实现 MATLAB 轨迹数据正确传递给 Simulink

**具体步骤**:
1. **标准化数据格式**
   ```matlab
   % 在 planTrajectory.m 中修改输出格式
   % 将轨迹数据转换为 timeseries 对象
   time_vector = linspace(0, T_total, size(Q_smooth, 1))';
   traj_timeseries = timeseries([time_vector, Q_smooth], time_vector);
   ```

2. **修改 runSimulink.m**
   ```matlab
   % 使用标准的 timeseries 格式
   trajDataRight = timeseries(qMatR, t_all);
   trajDataLeft = timeseries(qMatL, t_all);
   assignin('base', 'trajDataRight', trajDataRight);
   assignin('base', 'trajDataLeft', trajDataLeft);
   ```

3. **Simulink 模块配置**
   - 在 YumiSimscape.slx 中使用 `From Workspace` 模块
   - 设置数据格式为 `timeseries`
   - 连接 `Simulink-PS Converter` 到关节输入

#### 任务 1.2：统一坐标系基准
**目标**: 解决 MATLAB 与 Simulink 坐标系不一致问题

**具体步骤**:
1. **建立统一参考系**
   - 以 `World Frame` 为绝对基准
   - 在 setupRobotEnv.m 中添加坐标变换矩阵
   ```matlab
   % 定义 YuMi 基座到世界坐标系的变换
   yumi_base_transform = trvec2tform([0.5, 0, 0.06]);
   ```

2. **修改 lego_config.m 坐标定义**
   - 确保所有 LEGO 坐标都在 World Frame 下定义
   - 验证第一层12个积木的精确位置

3. **添加坐标验证机制**
   ```matlab
   % 在 main.m 中添加坐标验证
   verify_coordinates(brick_config, yumi);
   ```

#### 任务 1.3：优化关节驱动接口
**目标**: 确保关节轨迹正确驱动 YuMi 模型

**具体步骤**:
1. **配置关节模块**
   - 在 YumiSimscape.slx 中设置每个关节的 `Actuation > Motion` 为 `Provided by Input`
   - 确保信号单位为弧度 (rad)

2. **添加信号处理**
   - 使用 `Demux` 模块分解7个关节信号
   - 为每个关节添加独立的 `Simulink-PS Converter`

### 阶段二：夹爪控制系统完善

#### 任务 2.1：实现夹爪开合控制
**目标**: 精确控制夹爪动作时机

**具体步骤**:
1. **生成夹爪控制信号**
   ```matlab
   % 在 planTrajectory.m 中添加
   gripper_signal = generate_gripper_timeline(traj_timeline);
   gripper_timeseries = timeseries(gripper_signal, time_vector);
   ```

2. **Simulink 夹爪控制**
   - 添加夹爪控制的 `From Workspace` 模块
   - 实现夹爪关节的位置控制

#### 任务 2.2：物理接触模拟
**目标**: 模拟夹爪与积木的物理接触

**具体步骤**:
1. **添加接触力模块**
   - 在夹爪和积木间添加 `Spatial Contact Force`
   - 配置接触刚度和阻尼参数

2. **实现抓取逻辑**
   ```matlab
   % 基于夹爪状态控制接触力
   contact_enable = gripper_closed & contact_detected;
   ```

### 阶段三：物理仿真精度优化

#### 任务 3.1：LEGO 积木物理属性配置
**目标**: 实现精确的积木物理模拟

**具体步骤**:
1. **STL 文件配置**
   - 使用 `meshes/LEGO-2X3-S.stl` 文件
   - 在 `File Solid` 模块中配置几何体

2. **物理参数设置**
   ```matlab
   % LEGO 积木物理参数
   lego_mass = 0.0025;  % kg
   lego_density = 1040; % kg/m³ (ABS塑料)
   contact_stiffness = 1e6; % N/m
   contact_damping = 1e3;   % N*s/m
   ```

#### 任务 3.2：碰撞检测优化
**目标**: 实现精确的积木间碰撞检测

**具体步骤**:
1. **配置碰撞属性**
   - 为每个积木添加碰撞几何体
   - 设置合适的摩擦系数

2. **堆叠稳定性**
   - 调整接触参数确保堆叠稳定
   - 添加重力和惯性效果

### 阶段四：多层扩展和双臂协调

#### 任务 4.1：扩展到8层城堡
**目标**: 实现完整的8层城堡搭建

**具体步骤**:
1. **解析 mainbu.ldr 文件**
   ```matlab
   % 创建 parse_ldr_file.m
   castle_structure = parse_ldr_file('mainbu.ldr');
   ```

2. **生成多层轨迹**
   - 扩展 planTrajectory.m 支持多层
   - 实现层间的轨迹连接

#### 任务 4.2：双臂避障协调
**目标**: 实现双臂安全协作

**具体步骤**:
1. **工作空间划分**
   - 定义左右臂的安全工作区域
   - 实现时序协调避免碰撞

2. **路径优化**
   - 可选：集成 RRT 算法实现避障
   - 优化轨迹平滑性和效率

### 阶段五：测试验证和数据输出

#### 任务 5.1：系统集成测试
**目标**: 验证完整系统功能

**具体步骤**:
1. **分层测试**
   - 先验证第一层搭建
   - 逐步扩展到完整城堡

2. **性能优化**
   - 调整仿真步长和求解器参数
   - 优化仿真速度和稳定性

#### 任务 5.2：数据记录和分析
**目标**: 实现论文级数据输出

**具体步骤**:
1. **添加数据记录模块**
   ```matlab
   % 在 Simulink 中添加 To Workspace 模块
   joint_angles_log = [];
   contact_forces_log = [];
   gripper_states_log = [];
   ```

2. **生成分析报告**
   - 关节轨迹分析
   - 接触力统计
   - 任务完成时间评估

---

## ⚠️ 关键问题解决方案

### 1. 轨迹数据格式问题
**解决方案**: 严格使用 `timeseries([time, data], time)` 格式，配合 `From Workspace` 模块

### 2. 坐标系不一致
**解决方案**: 建立以 `World Frame` 为基准的统一坐标系，使用 `Rigid Transform` 进行校正

### 3. LEGO CAD 导入
**解决方案**: 必须使用 STL 文件，配置 `File Solid` 模块的物理和碰撞属性

### 4. Simulink 模块配置
**解决方案**: 遵循 `From Workspace` → `Simulink-PS Converter` → `Joint Actuation` 的标准流程

---

## 📅 里程碑和时间规划

| 阶段 | 预计时间 | 关键里程碑 |
|------|----------|------------|
| 阶段一 | 1-2周 | 数据接口正常工作，坐标系统一 |
| 阶段二 | 1周 | 夹爪控制完善，基本抓取功能 |
| 阶段三 | 1-2周 | 物理仿真精确，第一层稳定搭建 |
| 阶段四 | 2-3周 | 8层城堡完整实现，双臂协调 |
| 阶段五 | 1周 | 系统测试完成，数据输出正常 |

**总预计时间**: 6-9周

---

## 🔧 技术配置指南

### MATLAB 环境要求
- Robotics System Toolbox
- Simscape Multibody
- Simulink

### 关键文件清单
- `YumiSimscape.slx`: YuMi 机器人模型
- `mainbu.ldr`: 城堡设计文件
- `meshes/LEGO-2X3-S.stl`: LEGO 积木 STL 文件
- `说明/積木座標.csv.xlsx`: 积木坐标数据

### 推荐开发流程
1. 先在 MATLAB 中验证轨迹规划
2. 逐步在 Simulink 中实现各模块
3. 分层测试，确保每层功能正常
4. 最后进行完整系统集成

---

## 🎯 预期成果

1. **完整的仿真系统**: 能够完整模拟 YuMi 双臂搭建8层城堡的全过程
2. **精确的动作控制**: 机器人动作流畅，积木放置精确
3. **丰富的数据输出**: 关节轨迹、接触力、时间分析等论文级数据
4. **可视化演示**: 清晰的3D动画展示搭建过程
5. **技术文档**: 完整的实现文档和使用说明

---

## 🔍 详细技术实现指南

### 城堡结构分析（基于 mainbu.ldr）

#### 第一层布局（12个积木）
根据代码分析，第一层采用6列布局：
```
列1(竖直) | 列2(水平) | 列3(水平) | 列4(水平) | 列5(水平) | 列6(水平) | 列7(竖直)
   B01    |  B03/B05 |  B07/B09 |  B04/B06 |  B08/B10 |  B11/B12 |   B02
```

**坐标计算公式**:
```matlab
% 基于 lego_config.m 的精确坐标
center_x = 0.5; center_y = 0;
Lx = 0.0318; Ly = 0.0159; % LEGO 尺寸
col_x(1) = center_x - 2*Lx - Lx/2 - Ly/2;  % 第1列(竖直)
col_x(2) = center_x - 2*Lx;                 % 第2列(水平)
col_x(3) = center_x - Lx;                   % 第3列(水平)
col_x(4) = center_x;                        % 第4列(水平)
col_x(5) = center_x + Lx;                   % 第5列(水平)
col_x(6) = center_x + 2*Lx;                 % 第6列(水平)
col_x(7) = center_x + 2*Lx + Lx/2 + Ly/2;  % 第7列(竖直)
```

#### 多层扩展策略
```matlab
% 基于 mainbu.ldr 解析的8层结构
layer_heights = table_z_surface + (0:7) * brick_height + brick_height/2;
layer_configs = parse_castle_layers('mainbu.ldr');
```

### 关键模块配置详解

#### 1. From Workspace 模块配置
```matlab
% 在 Simulink 中的具体设置
Block: From Workspace
Parameters:
  - Variable name: 'trajDataRight' / 'trajDataLeft'
  - Sample time: 0 (inherited)
  - Interpolate data: on
  - Output after final data value: Hold final value
```

#### 2. Simulink-PS Converter 配置
```matlab
% 物理信号转换器设置
Block: Simulink-PS Converter
Parameters:
  - Input signal unit: rad (for joint angles)
  - Filter input: off (for smooth trajectories)
```

#### 3. Joint Actuation 配置
```matlab
% 关节驱动设置
Block: Revolute Joint
Parameters:
  - Actuation > Motion: Provided by Input
  - Sensing: Position and Velocity (for data logging)
  - Internal Mechanics: Damping = 0.1, Spring = 0
```

### 夹爪控制详细实现

#### 夹爪状态机设计
```matlab
% 在 planTrajectory.m 中实现
gripper_states = {
    'open',     % 0: 夹爪张开
    'closing',  % 0.5: 夹爪闭合中
    'closed',   % 1: 夹爪闭合
    'opening'   % 0.5: 夹爪张开中
};

% 时间同步的夹爪控制
function gripper_timeline = generate_gripper_timeline(traj_phases)
    gripper_timeline = zeros(size(traj_phases));
    for i = 1:length(traj_phases)
        switch traj_phases{i}
            case 'approach_pick'
                gripper_timeline(i) = 0;  % 张开
            case 'pick'
                gripper_timeline(i) = 1;  % 闭合
            case 'place'
                gripper_timeline(i) = 0;  % 张开
        end
    end
end
```

#### 物理接触实现
```matlab
% Spatial Contact Force 模块配置
Contact_Parameters = struct(...
    'Stiffness', 1e6, ...      % N/m
    'Damping', 1e3, ...        % N*s/m
    'StaticFriction', 0.8, ... % 静摩擦系数
    'KineticFriction', 0.6, ...% 动摩擦系数
    'Transition_Velocity', 0.01 % m/s
);
```

### 数据记录和分析系统

#### 关键数据记录
```matlab
% 在 Simulink 中添加 To Workspace 模块记录
logged_data = struct(...
    'joint_angles', [], ...     % 关节角度 [time, q1-q14]
    'joint_velocities', [], ... % 关节速度
    'end_effector_poses', [], ...% 末端执行器位姿
    'contact_forces', [], ...   % 接触力
    'gripper_states', [], ...   % 夹爪状态
    'lego_positions', []        % LEGO 积木位置
);
```

#### 性能评估指标
```matlab
% 任务完成质量评估
performance_metrics = struct(...
    'position_accuracy', [], ...    % 位置精度 (mm)
    'orientation_accuracy', [], ... % 姿态精度 (deg)
    'task_completion_time', [], ... % 任务完成时间 (s)
    'collision_events', [], ...     % 碰撞事件数
    'force_profiles', []            % 力曲线分析
);
```

---

## ⚡ 风险评估与应对策略

### 高风险项目

#### 1. 数据接口兼容性问题
**风险描述**: MATLAB 生成的轨迹数据格式与 Simulink 期望格式不匹配
**影响程度**: 高 - 可能导致仿真无法运行
**应对策略**:
- 建立数据格式验证机制
- 创建标准化的数据转换函数
- 提供多种格式的备用方案

#### 2. 坐标系转换错误
**风险描述**: MATLAB 和 Simulink 坐标系不一致导致位置偏差
**影响程度**: 高 - 影响积木放置精度
**应对策略**:
- 建立坐标系验证测试
- 使用可视化工具验证坐标转换
- 实现坐标系自动校准功能

#### 3. 物理仿真不稳定
**风险描述**: 积木堆叠过程中出现物理不稳定现象
**影响程度**: 中 - 影响仿真可靠性
**应对策略**:
- 调整求解器参数和步长
- 优化接触参数设置
- 实现渐进式参数调优

### 中风险项目

#### 4. 夹爪控制精度不足
**风险描述**: 夹爪开合时机不准确，影响抓取成功率
**影响程度**: 中 - 影响任务完成质量
**应对策略**:
- 实现力反馈控制
- 添加接触检测传感器
- 优化夹爪控制算法

#### 5. 双臂协调冲突
**风险描述**: 双臂运动路径冲突导致碰撞
**影响程度**: 中 - 影响系统安全性
**应对策略**:
- 实现工作空间分离
- 添加碰撞检测算法
- 优化任务调度策略

### 低风险项目

#### 6. 仿真性能问题
**风险描述**: 复杂模型导致仿真速度过慢
**影响程度**: 低 - 影响开发效率
**应对策略**:
- 优化模型复杂度
- 使用并行计算
- 实现分层仿真策略

---

## 📊 质量保证和测试策略

### 单元测试
1. **轨迹规划测试**: 验证生成的轨迹平滑性和可达性
2. **坐标转换测试**: 验证 MATLAB 和 Simulink 坐标一致性
3. **夹爪控制测试**: 验证夹爪开合时机和力度控制
4. **物理仿真测试**: 验证积木物理属性和碰撞检测

### 集成测试
1. **单层搭建测试**: 验证第一层12个积木的完整搭建
2. **多层扩展测试**: 验证2-8层的逐层搭建
3. **双臂协调测试**: 验证左右臂的协作和避障
4. **完整系统测试**: 验证8层城堡的完整搭建过程

### 性能测试
1. **精度测试**: 测量积木放置的位置和姿态精度
2. **稳定性测试**: 验证长时间仿真的稳定性
3. **效率测试**: 测量任务完成时间和资源消耗

---

## 🎓 学习资源和参考文档

### 官方文档
1. [MathWorks Simscape Multibody 用户指南](https://www.mathworks.com/help/sm/)
2. [机械臂建模和控制教程](https://uk.mathworks.com/help/robotics/ug/model-and-control-a-manipulator-arm-with-simscape.html)
3. [Robotics System Toolbox 文档](https://www.mathworks.com/help/robotics/)

### 技术论文
1. "Collaborative Robot Path Planning for Assembly Tasks"
2. "Physics-based Simulation of Robotic Manipulation"
3. "Multi-arm Coordination in Robotic Assembly"

### 开发工具
1. **LeoCAD**: LEGO 模型设计和可视化
2. **Simscape Multibody**: 物理仿真环境
3. **MATLAB Robotics Toolbox**: 机器人学算法库

---

此详细任务书基于对您现有代码库的深入分析，提供了完整的实施路径、风险评估和质量保证策略。建议按阶段顺序执行，确保每个里程碑的达成，最终实现高质量的 YuMi 双臂机器人 LEGO 城堡堆叠仿真系统。
