%% YuMi LEGO Project Environment Verification Script
% Verify all necessary toolboxes, files and configurations

clc; clear; close all;
fprintf('=== YuMi LEGO Project Environment Verification ===\n\n');

%% 1. Check required toolboxes
fprintf('1. Checking toolbox licenses...\n');
required_toolboxes = {
    'Robotics_System_Toolbox', 'Robotics System Toolbox';
    'Simscape', 'Simscape';
    'Simscape_Multibody', 'Simscape Multibody';
    'Simulink', 'Simulink'
};

toolbox_ok = true;
for i = 1:size(required_toolboxes, 1)
    if license('test', required_toolboxes{i,1})
        fprintf('   ✓ %s installed\n', required_toolboxes{i,2});
    else
        fprintf('   ❌ %s not installed or no license\n', required_toolboxes{i,2});
        toolbox_ok = false;
    end
end

%% 2. Check key files
fprintf('\n2. Checking key files...\n');
key_files = {
    'YumiSimscape.slx', 'YuMi Simulink Model';
    'Yumi_Lego_Final_Simulation.slx', 'Final Simulation Model';
    'mainbu.ldr', 'Castle Design File';
    'meshes/LEGO-2X3-S.stl', 'LEGO STL File';
    'lego_config.m', 'LEGO Configuration Script';
    'planTrajectory.m', 'Trajectory Planning Script';
    'setupRobotEnv.m', 'Environment Setup Script'
};

files_ok = true;
for i = 1:size(key_files, 1)
    if exist(key_files{i,1}, 'file')
        fprintf('   ✓ %s exists\n', key_files{i,2});
    else
        fprintf('   ❌ %s missing\n', key_files{i,2});
        files_ok = false;
    end
end

%% 3. Test robot environment setup
fprintf('\n3. Testing robot environment setup...\n');
try
    [yumi, qHome, table, ax] = setupRobotEnv();
    fprintf('   ✓ Robot environment setup successful\n');
    fprintf('   ✓ YuMi model loaded, joint count: %d\n', length(qHome));
    robot_ok = true;
catch ME
    fprintf('   ❌ Robot environment setup failed: %s\n', ME.message);
    robot_ok = false;
end

%% 4. Test LEGO configuration
fprintf('\n4. Testing LEGO configuration...\n');
try
    brick_config = lego_config();
    fprintf('   ✓ LEGO configuration loaded successfully\n');
    fprintf('   ✓ Target positions count: %d\n', size(brick_config.all_targets, 1));
    fprintf('   ✓ Task sequence count: %d\n', length(brick_config.task_sequence));
    fprintf('   ✓ Right arm initial bricks: %d\n', size(brick_config.right_arm_initial, 1));
    fprintf('   ✓ Left arm initial bricks: %d\n', size(brick_config.left_arm_initial, 1));
    lego_ok = true;
catch ME
    fprintf('   ❌ LEGO configuration failed: %s\n', ME.message);
    lego_ok = false;
end

%% 5. Check Simulink models
fprintf('\n5. Checking Simulink models...\n');
try
    % Check YumiSimscape model
    if ~bdIsLoaded('YumiSimscape')
        load_system('YumiSimscape.slx');
    end
    fprintf('   ✓ YumiSimscape.slx model loaded\n');
    
    % Check final simulation model
    if ~bdIsLoaded('Yumi_Lego_Final_Simulation')
        load_system('Yumi_Lego_Final_Simulation.slx');
    end
    fprintf('   ✓ Yumi_Lego_Final_Simulation.slx model loaded\n');
    simulink_ok = true;
catch ME
    fprintf('   ❌ Simulink model loading failed: %s\n', ME.message);
    simulink_ok = false;
end

%% 6. Summary of verification results
fprintf('\n=== Verification Results Summary ===\n');
if toolbox_ok && files_ok && robot_ok && lego_ok && simulink_ok
    fprintf('🎉 All verifications passed! Ready to start Phase 1 tasks.\n');
    verification_passed = true;
else
    fprintf('⚠️  Issues found that need resolution:\n');
    if ~toolbox_ok, fprintf('   - Toolbox license issues\n'); end
    if ~files_ok, fprintf('   - Missing key files\n'); end
    if ~robot_ok, fprintf('   - Robot environment issues\n'); end
    if ~lego_ok, fprintf('   - LEGO configuration issues\n'); end
    if ~simulink_ok, fprintf('   - Simulink model issues\n'); end
    verification_passed = false;
end

%% 7. If verification passed, run coordinate verification
if verification_passed && exist('brick_config', 'var') && exist('yumi', 'var')
    fprintf('\n=== Coordinate System Verification ===\n');
    verify_coordinates(brick_config, yumi);
end

fprintf('\nVerification complete!\n');
