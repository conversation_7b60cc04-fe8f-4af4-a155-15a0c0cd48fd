#!/usr/bin/env python3
"""
LEGO积木坐标分析脚本
读取并分析CSV文件中的LEGO积木坐标数据

作者: AI Assistant
日期: 2025-07-27
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

def read_excel_file(file_path):
    """读取Excel文件并返回DataFrame"""
    try:
        df = pd.read_excel(file_path)
        print(f"✅ 成功读取: {file_path}")
        print(f"  包含 {len(df)} 行数据")
        print(f"  列名: {', '.join(str(col) for col in df.columns)}")
        
        # 打印前几行数据以了解结构
        print("\n📊 前5行数据预览:")
        print(df.head().to_string())
        
        return df
    except Exception as e:
        print(f"❌ 无法读取 {file_path}: {str(e)}")
        return None

def clean_dataframe(df):
    """清理DataFrame数据，转换为可处理的格式"""
    # 复制DataFrame以避免修改原始数据
    df_cleaned = df.copy()
    
    # 检查是否有标题行或头部非数据行
    # 通常第一行可能包含列标题，我们需要找到实际数据开始的行
    
    # 尝试查找包含'x'、'y'、'z'或坐标相关字段的行
    coord_rows = []
    for i, row in df.iterrows():
        row_str = ' '.join(str(val).lower() for val in row.values)
        if any(term in row_str for term in ['x', 'y', 'z', 'coord', '坐标', 'type']):
            coord_rows.append(i)
    
    if coord_rows:
        # 使用最后一个包含坐标相关词的行作为标题行
        header_row = max(coord_rows)
        # 重新设置列名并从该行之后开始提取数据
        df_cleaned = df.iloc[(header_row+1):].reset_index(drop=True)
        df_cleaned.columns = df.iloc[header_row].values
    
    print("\n🧹 数据清理:")
    print(f"  识别到的列名: {', '.join(str(col) for col in df_cleaned.columns)}")
    print("\n  清理后前5行数据:")
    print(df_cleaned.head().to_string())
    
    return df_cleaned

def identify_coordinate_columns(df):
    """识别坐标列"""
    # 查找可能的坐标列名
    x_keywords = ['x', 'X', '横坐标', 'Unnamed: 1']
    y_keywords = ['y', 'Y', '纵坐标', 'Unnamed: 2']
    z_keywords = ['z', 'Z', '高度', 'Unnamed: 3']
    
    x_col = None
    y_col = None
    z_col = None
    
    # 尝试根据列名匹配
    for col in df.columns:
        col_str = str(col).lower()
        if any(keyword.lower() in col_str for keyword in x_keywords):
            x_col = col
        elif any(keyword.lower() in col_str for keyword in y_keywords):
            y_col = col
        elif any(keyword.lower() in col_str for keyword in z_keywords):
            z_col = col
    
    # 如果没有找到，尝试使用前三个数值列
    if not (x_col and y_col and z_col):
        numeric_cols = [col for col in df.columns 
                        if pd.api.types.is_numeric_dtype(df[col])]
        if len(numeric_cols) >= 3:
            x_col = numeric_cols[0]
            y_col = numeric_cols[1]
            z_col = numeric_cols[2]
    
    # 如果仍然没有找到，尝试使用位置索引
    if not (x_col and y_col and z_col):
        cols = list(df.columns)
        if len(cols) >= 4:  # 假设第一列是名称或索引
            x_col = cols[1]
            y_col = cols[2]
            z_col = cols[3]
    
    return x_col, y_col, z_col

def extract_coordinate_data(df, x_col, y_col, z_col):
    """从DataFrame提取坐标数据"""
    if not (x_col and y_col and z_col):
        print("❌ 无法识别坐标列")
        return None, None, None
    
    print(f"📍 识别到的坐标列: X={x_col}, Y={y_col}, Z={z_col}")
    
    try:
        # 使用pd.to_numeric尝试转换非数值数据
        x_data = pd.to_numeric(df[x_col], errors='coerce').fillna(0)
        y_data = pd.to_numeric(df[y_col], errors='coerce').fillna(0)
        z_data = pd.to_numeric(df[z_col], errors='coerce').fillna(0)
        
        # 过滤掉全为0的点（可能是由转换错误导致的）
        valid_points = (x_data != 0) | (y_data != 0) | (z_data != 0)
        x_data = x_data[valid_points]
        y_data = y_data[valid_points]
        z_data = z_data[valid_points]
        
        if len(x_data) == 0:
            print("❌ 未找到有效坐标点")
            return None, None, None
        
        print(f"✅ 成功提取 {len(x_data)} 个有效坐标点")
        print(f"坐标范围:")
        print(f"  X: {x_data.min():.4f} 到 {x_data.max():.4f}")
        print(f"  Y: {y_data.min():.4f} 到 {y_data.max():.4f}")
        print(f"  Z: {z_data.min():.4f} 到 {z_data.max():.4f}")
        
        return x_data, y_data, z_data
    except Exception as e:
        print(f"❌ 提取坐标数据时出错: {str(e)}")
        return None, None, None

def analyze_coordinates(df):
    """分析坐标数据"""
    # 清理数据
    df_cleaned = clean_dataframe(df)
    
    # 识别坐标列
    x_col, y_col, z_col = identify_coordinate_columns(df_cleaned)
    
    # 提取坐标数据
    return extract_coordinate_data(df_cleaned, x_col, y_col, z_col)

def visualize_coordinates(df, title, save_path=None):
    """可视化坐标数据"""
    x_data, y_data, z_data = analyze_coordinates(df)
    
    if x_data is None or y_data is None or z_data is None:
        print("❌ 无法可视化坐标数据")
        return
    
    fig = plt.figure(figsize=(10, 8))
    ax = fig.add_subplot(111, projection='3d')
    
    # 绘制散点图
    scatter = ax.scatter(x_data, y_data, z_data, c=z_data, cmap='viridis', 
                         marker='o', s=100, alpha=0.8)
    
    # 添加坐标轴标签
    ax.set_xlabel('X 坐标')
    ax.set_ylabel('Y 坐标')
    ax.set_zlabel('Z 坐标')
    
    # 添加标题
    ax.set_title(f'LEGO积木坐标可视化 - {title}')
    
    # 添加颜色条
    cb = plt.colorbar(scatter)
    cb.set_label('Z 高度')
    
    # 调整视角
    ax.view_init(elev=30, azim=45)
    
    # 保存或显示图像
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"✅ 可视化图像已保存到: {save_path}")
    else:
        plt.show()
    
    plt.close(fig)

def list_coordinate_points(df, max_points=20):
    """列出坐标点数据"""
    x_data, y_data, z_data = analyze_coordinates(df)
    
    if x_data is None or y_data is None or z_data is None:
        return
    
    print("\n📊 坐标点列表:")
    print(f"{'索引':^6}{'X坐标':^12}{'Y坐标':^12}{'Z坐标':^12}")
    print("-" * 42)
    
    for i in range(min(len(x_data), max_points)):
        print(f"{i:^6}{x_data.iloc[i]:^12.4f}{y_data.iloc[i]:^12.4f}{z_data.iloc[i]:^12.4f}")
    
    if len(x_data) > max_points:
        print(f"... 还有 {len(x_data) - max_points} 个点 ...")

def analyze_building_structure(df):
    """分析构建结构"""
    x_data, y_data, z_data = analyze_coordinates(df)
    
    if x_data is None or y_data is None or z_data is None:
        return
    
    # 尝试确定层级
    unique_z = sorted(z_data.unique())
    
    print("\n🏗️ 构建结构分析:")
    print(f"检测到的不同高度层级: {len(unique_z)}")
    
    for i, z_level in enumerate(unique_z):
        level_points = z_data[z_data == z_level].count()
        print(f"  层级 {i+1} (Z={z_level:.4f}): {level_points} 个积木")

def try_alternative_approach(file_path):
    """尝试替代方法读取Excel文件"""
    print("\n🔄 尝试替代方法读取文件...")
    try:
        # 直接读取特定的列和行，跳过标题
        df = pd.read_excel(file_path, header=None, usecols=[1, 2, 3], skiprows=1)
        df.columns = ['X', 'Y', 'Z']
        
        # 转换为数值型并过滤无效数据
        df = df.apply(pd.to_numeric, errors='coerce')
        df = df.dropna()
        
        print(f"✅ 使用替代方法成功读取 {len(df)} 行有效数据")
        print("\n📊 前5行数据预览:")
        print(df.head().to_string())
        
        # 如果有足够的有效数据，绘制散点图
        if len(df) > 3:
            fig = plt.figure(figsize=(10, 8))
            ax = fig.add_subplot(111, projection='3d')
            
            scatter = ax.scatter(df['X'], df['Y'], df['Z'], c=df['Z'], 
                                cmap='viridis', marker='o', s=100, alpha=0.8)
            
            ax.set_xlabel('X 坐标')
            ax.set_ylabel('Y 坐标')
            ax.set_zlabel('Z 坐标')
            
            # 添加标题
            ax.set_title(f'LEGO积木坐标可视化 (替代方法) - {os.path.basename(file_path)}')
            
            # 添加颜色条
            cb = plt.colorbar(scatter)
            cb.set_label('Z 高度')
            
            # 调整视角
            ax.view_init(elev=30, azim=45)
            
            # 保存图像
            output_filename = f"lego_coordinates_alt_{os.path.basename(file_path).split('.')[0]}.png"
            plt.savefig(output_filename, dpi=300, bbox_inches='tight')
            print(f"✅ 可视化图像已保存到: {output_filename}")
            
            plt.close(fig)
        
        return True
    except Exception as e:
        print(f"❌ 替代方法失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🧩 LEGO积木坐标分析工具")
    print("=" * 50)
    
    # 定义Excel文件路径
    excel_files = [
        '说明/積木座標.csv.xlsx',
        '说明/第一層積木.csv.xlsx',
        '说明/積木座標.csv.xlsx',
    ]
    
    for file_path in excel_files:
        if os.path.exists(file_path):
            print(f"\n📄 分析文件: {file_path}")
            print("-" * 40)
            
            df = read_excel_file(file_path)
            if df is not None:
                try:
                    # 标准分析流程
                    visualize_coordinates(df, os.path.basename(file_path), 
                                         save_path=f"lego_coordinates_{os.path.basename(file_path).split('.')[0]}.png")
                except:
                    print("❌ 标准分析方法失败")
                    # 尝试替代方法
                    try_alternative_approach(file_path)
                
                print("\n" + "-" * 40)
        else:
            print(f"\n❌ 文件不存在: {file_path}")
    
    print("\n✅ 分析完成")

if __name__ == "__main__":
    main() 