function verify_coordinates(brick_config, yumi)
%% 坐标系验证函数
% 验证 MATLAB 和 Simulink 坐标系的一致性

fprintf('\n=== 坐标系验证 ===\n');

% 检查第一层积木坐标
targets = brick_config.all_targets;
fprintf('第一层积木数量: %d\n', size(targets, 1));

% 显示坐标范围
x_range = [min(targets(:,1)), max(targets(:,1))];
y_range = [min(targets(:,2)), max(targets(:,2))];
z_range = [min(targets(:,3)), max(targets(:,3))];

fprintf('X 坐标范围: [%.3f, %.3f] m\n', x_range);
fprintf('Y 坐标范围: [%.3f, %.3f] m\n', y_range);
fprintf('Z 坐标范围: [%.3f, %.3f] m\n', z_range);

% 检查是否在工作空间内
workspace_x = [0.35, 0.65];  % 城堡区域
workspace_y = [-0.1, 0.1];

if x_range(1) >= workspace_x(1) && x_range(2) <= workspace_x(2) && ...
   y_range(1) >= workspace_y(1) && y_range(2) <= workspace_y(2)
    fprintf('✓ 所有积木都在工作空间内\n');
else
    fprintf('❌ 部分积木超出工作空间\n');
    fprintf('   工作空间 X: [%.3f, %.3f]\n', workspace_x);
    fprintf('   工作空间 Y: [%.3f, %.3f]\n', workspace_y);
end

% 检查初始积木位置
fprintf('\n--- 初始积木位置检查 ---\n');
right_initial = brick_config.right_arm_initial;
left_initial = brick_config.left_arm_initial;

fprintf('右手积木区域:\n');
for i = 1:size(right_initial, 1)
    pos = right_initial{i, 2};
    fprintf('   R%d: [%.3f, %.3f, %.3f]\n', i, pos);
end

fprintf('左手积木区域:\n');
for i = 1:size(left_initial, 1)
    pos = left_initial{i, 2};
    fprintf('   L%d: [%.3f, %.3f, %.3f]\n', i, pos);
end

% 检查任务序列
fprintf('\n--- 任务序列检查 ---\n');
task_sequence = brick_config.task_sequence;
fprintf('前5个任务:\n');
for i = 1:min(5, length(task_sequence))
    task = task_sequence(i);
    target_pos = targets(task.target_id, 1:3);
    fprintf('   任务%d: %s手臂 -> 目标[%.3f, %.3f, %.3f]\n', ...
        i, task.arm, target_pos);
end

% 可视化坐标分布
try
    figure('Name', '积木坐标分布验证', 'Position', [100, 100, 800, 600]);
    
    % 绘制目标位置
    subplot(2,2,1);
    scatter3(targets(:,1), targets(:,2), targets(:,3), 100, 'r', 'filled');
    xlabel('X (m)'); ylabel('Y (m)'); zlabel('Z (m)');
    title('第一层积木目标位置');
    grid on; axis equal;
    
    % 绘制右手初始位置
    subplot(2,2,2);
    right_pos = cell2mat(cellfun(@(x) x, right_initial(:,2), 'UniformOutput', false));
    scatter3(right_pos(:,1), right_pos(:,2), right_pos(:,3), 100, 'b', 'filled');
    xlabel('X (m)'); ylabel('Y (m)'); zlabel('Z (m)');
    title('右手初始积木位置');
    grid on; axis equal;
    
    % 绘制左手初始位置
    subplot(2,2,3);
    left_pos = cell2mat(cellfun(@(x) x, left_initial(:,2), 'UniformOutput', false));
    scatter3(left_pos(:,1), left_pos(:,2), left_pos(:,3), 100, 'g', 'filled');
    xlabel('X (m)'); ylabel('Y (m)'); zlabel('Z (m)');
    title('左手初始积木位置');
    grid on; axis equal;
    
    % 绘制整体布局
    subplot(2,2,4);
    scatter3(targets(:,1), targets(:,2), targets(:,3), 100, 'r', 'filled'); hold on;
    scatter3(right_pos(:,1), right_pos(:,2), right_pos(:,3), 80, 'b', 'filled');
    scatter3(left_pos(:,1), left_pos(:,2), left_pos(:,3), 80, 'g', 'filled');
    
    % 绘制工作区域边界
    workspace_corners = [
        workspace_x(1), workspace_y(1), z_range(1);
        workspace_x(2), workspace_y(1), z_range(1);
        workspace_x(2), workspace_y(2), z_range(1);
        workspace_x(1), workspace_y(2), z_range(1);
        workspace_x(1), workspace_y(1), z_range(1)
    ];
    plot3(workspace_corners(:,1), workspace_corners(:,2), workspace_corners(:,3), 'k--', 'LineWidth', 2);
    
    xlabel('X (m)'); ylabel('Y (m)'); zlabel('Z (m)');
    title('整体工作空间布局');
    legend('目标位置', '右手积木', '左手积木', '工作区域', 'Location', 'best');
    grid on; axis equal;
    
    fprintf('✓ 坐标可视化图表已生成\n');
catch ME
    fprintf('❌ 可视化失败: %s\n', ME.message);
end

% 验证机器人可达性（简单检查）
fprintf('\n--- 机器人可达性检查 ---\n');
try
    % 检查几个关键位置的可达性
    test_positions = [
        targets(1, 1:3);  % 第一个目标位置
        right_initial{1, 2};  % 第一个右手积木
        left_initial{1, 2};   % 第一个左手积木
    ];
    
    position_names = {'第一个目标', '右手积木1', '左手积木1'};
    
    for i = 1:size(test_positions, 1)
        pos = test_positions(i, :);
        % 简单的距离检查（YuMi 工作半径约 0.5m）
        distance_from_base = norm(pos - [0.5, 0, 0.06]);  % 假设基座在 [0.5, 0, 0.06]
        
        if distance_from_base <= 0.6  % YuMi 最大工作半径
            fprintf('   ✓ %s 位置可达 (距离: %.3f m)\n', position_names{i}, distance_from_base);
        else
            fprintf('   ❌ %s 位置可能不可达 (距离: %.3f m)\n', position_names{i}, distance_from_base);
        end
    end
catch ME
    fprintf('❌ 可达性检查失败: %s\n', ME.message);
end

fprintf('\n坐标验证完成！\n');
end
