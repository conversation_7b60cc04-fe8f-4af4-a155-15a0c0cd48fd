%% YuMi LEGO 项目环境验证脚本
% 验证所有必要的工具箱、文件和配置是否正确

clc; clear; close all;
fprintf('=== YuMi LEGO 项目环境验证 ===\n\n');

%% 1. 检查必要的工具箱
fprintf('1. 检查工具箱许可...\n');
required_toolboxes = {
    'Robotics_System_Toolbox', 'Robotics System Toolbox';
    'Simscape', 'Simscape';
    'Simscape_Multibody', 'Simscape Multibody';
    'Simulink', 'Simulink'
};

toolbox_ok = true;
for i = 1:size(required_toolboxes, 1)
    if license('test', required_toolboxes{i,1})
        fprintf('   ✓ %s 已安装\n', required_toolboxes{i,2});
    else
        fprintf('   ❌ %s 未安装或无许可\n', required_toolboxes{i,2});
        toolbox_ok = false;
    end
end

%% 2. 检查关键文件
fprintf('\n2. 检查关键文件...\n');
key_files = {
    'YumiSimscape.slx', 'YuMi Simulink 模型';
    'Yumi_Lego_Final_Simulation.slx', '最终仿真模型';
    'mainbu.ldr', '城堡设计文件';
    'meshes/LEGO-2X3-S.stl', 'LEGO STL 文件';
    'lego_config.m', 'LEGO 配置脚本';
    'planTrajectory.m', '轨迹规划脚本';
    'setupRobotEnv.m', '环境设置脚本'
};

files_ok = true;
for i = 1:size(key_files, 1)
    if exist(key_files{i,1}, 'file')
        fprintf('   ✓ %s 存在\n', key_files{i,2});
    else
        fprintf('   ❌ %s 缺失\n', key_files{i,2});
        files_ok = false;
    end
end

%% 3. 测试机器人环境设置
fprintf('\n3. 测试机器人环境设置...\n');
try
    [yumi, qHome, table, ax] = setupRobotEnv();
    fprintf('   ✓ 机器人环境设置成功\n');
    fprintf('   ✓ YuMi 模型已加载，关节数: %d\n', length(qHome));
    robot_ok = true;
catch ME
    fprintf('   ❌ 机器人环境设置失败: %s\n', ME.message);
    robot_ok = false;
end

%% 4. 测试 LEGO 配置
fprintf('\n4. 测试 LEGO 配置...\n');
try
    brick_config = lego_config();
    fprintf('   ✓ LEGO 配置加载成功\n');
    fprintf('   ✓ 目标位置数量: %d\n', size(brick_config.all_targets, 1));
    fprintf('   ✓ 任务序列数量: %d\n', length(brick_config.task_sequence));
    fprintf('   ✓ 右手初始积木: %d 个\n', size(brick_config.right_arm_initial, 1));
    fprintf('   ✓ 左手初始积木: %d 个\n', size(brick_config.left_arm_initial, 1));
    lego_ok = true;
catch ME
    fprintf('   ❌ LEGO 配置失败: %s\n', ME.message);
    lego_ok = false;
end

%% 5. 检查 Simulink 模型
fprintf('\n5. 检查 Simulink 模型...\n');
try
    % 检查 YumiSimscape 模型
    if ~bdIsLoaded('YumiSimscape')
        load_system('YumiSimscape.slx');
    end
    fprintf('   ✓ YumiSimscape.slx 模型已加载\n');
    
    % 检查最终仿真模型
    if ~bdIsLoaded('Yumi_Lego_Final_Simulation')
        load_system('Yumi_Lego_Final_Simulation.slx');
    end
    fprintf('   ✓ Yumi_Lego_Final_Simulation.slx 模型已加载\n');
    simulink_ok = true;
catch ME
    fprintf('   ❌ Simulink 模型加载失败: %s\n', ME.message);
    simulink_ok = false;
end

%% 6. 总结验证结果
fprintf('\n=== 验证结果总结 ===\n');
if toolbox_ok && files_ok && robot_ok && lego_ok && simulink_ok
    fprintf('🎉 所有验证通过！可以开始实施阶段一任务。\n');
    verification_passed = true;
else
    fprintf('⚠️  存在问题需要解决：\n');
    if ~toolbox_ok, fprintf('   - 工具箱许可问题\n'); end
    if ~files_ok, fprintf('   - 关键文件缺失\n'); end
    if ~robot_ok, fprintf('   - 机器人环境问题\n'); end
    if ~lego_ok, fprintf('   - LEGO 配置问题\n'); end
    if ~simulink_ok, fprintf('   - Simulink 模型问题\n'); end
    verification_passed = false;
end

fprintf('\n验证完成！\n');
