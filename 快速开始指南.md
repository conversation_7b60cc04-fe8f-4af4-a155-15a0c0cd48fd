# YuMi LEGO 城堡项目快速开始指南

## 🚀 立即开始

### 第一步：验证当前环境
```matlab
% 在 MATLAB 命令窗口运行
clc; clear;

% 检查必要的工具箱
required_toolboxes = {'Robotics System Toolbox', 'Simscape Multibody'};
for i = 1:length(required_toolboxes)
    if license('test', strrep(required_toolboxes{i}, ' ', '_'))
        fprintf('✓ %s 已安装\n', required_toolboxes{i});
    else
        fprintf('❌ %s 未安装\n', required_toolboxes{i});
    end
end

% 检查关键文件
key_files = {'YumiSimscape.slx', 'mainbu.ldr', 'meshes/LEGO-2X3-S.stl'};
for i = 1:length(key_files)
    if exist(key_files{i}, 'file')
        fprintf('✓ %s 存在\n', key_files{i});
    else
        fprintf('❌ %s 缺失\n', key_files{i});
    end
end
```

### 第二步：运行基础测试
```matlab
% 测试机器人环境设置
try
    [yumi, qHome, table, ax] = setupRobotEnv();
    fprintf('✓ 机器人环境设置成功\n');
catch ME
    fprintf('❌ 机器人环境设置失败: %s\n', ME.message);
end

% 测试 LEGO 配置
try
    brick_config = lego_config();
    fprintf('✓ LEGO 配置加载成功，共 %d 个目标位置\n', size(brick_config.all_targets, 1));
catch ME
    fprintf('❌ LEGO 配置失败: %s\n', ME.message);
end
```

### 第三步：修复数据接口问题（阶段一优先任务）

#### 3.1 修改 runSimulink.m
创建新的数据格式转换函数：

```matlab
% 在 runSimulink.m 开头添加
function runSimulink_fixed(trajectories, T_total)
    modelName = 'YumiSimscape';
    
    % 确保模型已加载
    if ~bdIsLoaded(modelName)
        open_system([modelName '.slx']);
    end
    
    fprintf('\n=== 开始 Simulink 仿真 (修复版) ===\n');
    
    for i = 1:length(trajectories)
        traj = trajectories{i};
        Q = traj.Q_smooth;
        N = size(Q, 1);
        
        % 创建时间向量
        time_vector = linspace(0, T_total, N)';
        
        % 转换为 timeseries 格式
        if strcmp(traj.arm, 'right')
            trajDataRight = timeseries(Q, time_vector);
            trajDataLeft = timeseries(zeros(N, 7), time_vector);
        else
            trajDataRight = timeseries(zeros(N, 7), time_vector);
            trajDataLeft = timeseries(Q, time_vector);
        end
        
        % 传递到 base workspace
        assignin('base', 'trajDataRight', trajDataRight);
        assignin('base', 'trajDataLeft', trajDataLeft);
        assignin('base', 'T_total', T_total);
        
        % 设置仿真参数
        set_param(modelName, 'StopTime', num2str(T_total));
        
        % 运行仿真
        fprintf('→ 任务 %d/%d (%s)... ', i, length(trajectories), traj.arm);
        try
            simOut = sim(modelName);
            fprintf('完成\n');
        catch ME
            fprintf('失败: %s\n', ME.message);
        end
    end
end
```

#### 3.2 验证坐标系一致性
创建坐标验证函数：

```matlab
% 创建新文件：verify_coordinates.m
function verify_coordinates(brick_config, yumi)
    fprintf('\n=== 坐标系验证 ===\n');
    
    % 检查第一层积木坐标
    targets = brick_config.all_targets;
    fprintf('第一层积木数量: %d\n', size(targets, 1));
    
    % 显示坐标范围
    x_range = [min(targets(:,1)), max(targets(:,1))];
    y_range = [min(targets(:,2)), max(targets(:,2))];
    z_range = [min(targets(:,3)), max(targets(:,3))];
    
    fprintf('X 坐标范围: [%.3f, %.3f] m\n', x_range);
    fprintf('Y 坐标范围: [%.3f, %.3f] m\n', y_range);
    fprintf('Z 坐标范围: [%.3f, %.3f] m\n', z_range);
    
    % 检查是否在工作空间内
    workspace_x = [0.35, 0.65];  % 城堡区域
    workspace_y = [-0.1, 0.1];
    
    if x_range(1) >= workspace_x(1) && x_range(2) <= workspace_x(2) && ...
       y_range(1) >= workspace_y(1) && y_range(2) <= workspace_y(2)
        fprintf('✓ 所有积木都在工作空间内\n');
    else
        fprintf('❌ 部分积木超出工作空间\n');
    end
    
    % 可视化坐标分布
    figure('Name', '积木坐标分布');
    scatter3(targets(:,1), targets(:,2), targets(:,3), 100, 'filled');
    xlabel('X (m)'); ylabel('Y (m)'); zlabel('Z (m)');
    title('第一层积木目标位置');
    grid on; axis equal;
end
```

### 第四步：测试修复效果
```matlab
% 运行完整测试
clc; clear;

% 1. 环境设置
[yumi, qHome, table, ax] = setupRobotEnv();

% 2. LEGO 配置
brick_config = lego_config();

% 3. 坐标验证
verify_coordinates(brick_config, yumi);

% 4. 轨迹规划（仅前2个任务用于测试）
trajectories = planTrajectory(yumi, brick_config, qHome);

% 5. 运行修复版仿真
if ~isempty(trajectories)
    runSimulink_fixed(trajectories, 10);
else
    fprintf('❌ 轨迹规划失败，无法进行仿真测试\n');
end
```

## 🔧 常见问题解决

### 问题1：模型文件无法加载
```matlab
% 解决方案：重新生成 YuMi 模型
yumi = loadrobot('abbYumi','DataFormat','row','Gravity',[0 0 -9.81]);
exportrobot(yumi, 'yumi_exported.urdf');
smimport('yumi_exported.urdf', 'ModelName', 'YumiSimscape');
```

### 问题2：STL 文件路径错误
```matlab
% 检查 STL 文件
stl_file = 'meshes/LEGO-2X3-S.stl';
if ~exist(stl_file, 'file')
    fprintf('❌ STL 文件不存在: %s\n', stl_file);
    % 下载或创建 STL 文件
end
```

### 问题3：轨迹规划失败
```matlab
% 调试轨迹规划
try
    % 简化测试：只规划一个任务
    test_config = brick_config;
    test_config.task_sequence = test_config.task_sequence(1);
    test_trajectories = planTrajectory(yumi, test_config, qHome);
    
    if ~isempty(test_trajectories)
        fprintf('✓ 单任务轨迹规划成功\n');
    end
catch ME
    fprintf('❌ 轨迹规划错误: %s\n', ME.message);
    fprintf('   位置: %s, 行 %d\n', ME.stack(1).file, ME.stack(1).line);
end
```

## 📋 下一步行动计划

### 立即执行（今天）
1. ✅ 运行环境验证脚本
2. ✅ 测试基础功能
3. ✅ 实施数据接口修复

### 本周内完成
1. 🔄 完成阶段一的所有任务
2. 🔄 验证坐标系一致性
3. 🔄 测试单层积木搭建

### 下周目标
1. ⏳ 开始阶段二：夹爪控制
2. ⏳ 实现物理接触模拟
3. ⏳ 优化仿真稳定性

## 📞 技术支持

如果遇到问题，请按以下顺序检查：

1. **MATLAB 版本兼容性**: 确保使用 R2020b 或更新版本
2. **工具箱许可**: 验证 Robotics 和 Simscape 工具箱可用
3. **文件路径**: 确保所有文件在正确位置
4. **内存资源**: 复杂仿真需要足够的 RAM

### 调试技巧
```matlab
% 启用详细错误信息
dbstop if error

% 检查变量类型和大小
whos

% 保存工作空间用于调试
save('debug_workspace.mat');
```

---

**重要提醒**: 请按照任务书中的阶段顺序执行，确保每个阶段的目标达成后再进入下一阶段。优先解决数据接口问题，这是整个项目成功的关键基础。
