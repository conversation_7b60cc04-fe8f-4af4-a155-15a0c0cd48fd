#!/usr/bin/env python3
"""
LEGO城堡搭建系统演示脚本
展示基于参考图片的精确8层城堡搭建功能

作者: AI Assistant
日期: 2025-01-26
版本: 1.0
"""

import sys
import os
import time
import json
import numpy as np

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入核心模块
from castle_structure import CastleStructureDefinition
from yumi_controller import YuMiDualArmController, CartesianPose
from collision_detection import CollisionDetector
from stability_analyzer import StabilityAnalyzer
from visualization import CastleVisualizer

def demo_castle_structure():
    """演示城堡结构定义"""
    print("🏰 演示城堡结构定义")
    print("=" * 40)
    
    # 创建城堡结构
    castle = CastleStructureDefinition()
    
    print(f"根据参考图片定义的8层城堡结构:")
    print(f"  总积木数: {castle.get_total_brick_count()}")
    print(f"  总层数: {castle.get_level_count()}")
    
    # 显示各层详情
    for level in range(1, castle.get_level_count() + 1):
        level_bricks = castle.get_level_bricks(level)
        print(f"\n  Level {level}: {len(level_bricks)} 个积木")
        
        for i, brick in enumerate(level_bricks[:3]):  # 只显示前3个
            pos = brick['position']
            print(f"    {brick['id']}: 位置({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f})")
        
        if len(level_bricks) > 3:
            print(f"    ... 还有 {len(level_bricks) - 3} 个积木")
    
    # 导出结构
    castle.export_structure('demo_castle_structure.json')
    print(f"\n✅ 城堡结构已导出到: demo_castle_structure.json")
    
    return castle

def demo_yumi_control():
    """演示YuMi双臂控制"""
    print("\n🤖 演示YuMi双臂控制")
    print("=" * 40)
    
    # 创建控制器
    controller = YuMiDualArmController(
        ip_address="127.0.0.1",  # 演示模式使用本地IP
        left_enabled=True,
        right_enabled=True
    )
    
    print(f"YuMi控制器状态:")
    print(f"  连接状态: {controller.is_connected()}")
    print(f"  左臂启用: {controller.left_enabled}")
    print(f"  右臂启用: {controller.right_enabled}")
    
    # 演示拾取和放置操作
    print(f"\n演示积木拾取和放置操作:")
    
    # 积木供应位置
    supply_position = [0.3, -0.2, 0.05]
    target_position = [0.5, 0.0, 0.065]
    
    print(f"  1. 左臂拾取积木 (位置: {supply_position})")
    pick_success = controller.pick_brick("left", supply_position, "brick_2x4")
    print(f"     拾取结果: {'成功' if pick_success else '失败'}")
    
    if pick_success:
        print(f"  2. 左臂放置积木 (位置: {target_position})")
        place_success = controller.place_brick("left", target_position, 0)
        print(f"     放置结果: {'成功' if place_success else '失败'}")
    
    # 显示当前状态
    status = controller.get_status()
    print(f"\n当前系统状态:")
    print(f"  左臂位置: {status['left_arm']['pose'][:3]}")
    print(f"  右臂位置: {status['right_arm']['pose'][:3]}")
    print(f"  左夹爪: {status['left_arm']['gripper']}")
    print(f"  右夹爪: {status['right_arm']['gripper']}")
    
    controller.disconnect()
    print(f"✅ YuMi控制演示完成")
    
    return True

def demo_collision_detection():
    """演示碰撞检测"""
    print("\n🔍 演示碰撞检测")
    print("=" * 40)
    
    # 创建检测器
    detector = CollisionDetector(tolerance=1e-4)
    
    # 创建测试场景
    print("创建测试场景 - 4个积木的堆叠:")
    
    objects = {
        'base_1': {
            'position': [0.5, -0.016, 0.065],
            'orientation': 0,
            'size': [0.0318, 0.0159, 0.0096]
        },
        'base_2': {
            'position': [0.5, 0.016, 0.065],
            'orientation': 0,
            'size': [0.0318, 0.0159, 0.0096]
        },
        'top_1': {
            'position': [0.5, 0.0, 0.075],
            'orientation': 90,  # 旋转90度
            'size': [0.0318, 0.0159, 0.0096]
        },
        'collision_brick': {
            'position': [0.502, 0.0, 0.075],  # 故意设置碰撞位置
            'orientation': 0,
            'size': [0.0318, 0.0159, 0.0096]
        }
    }
    
    for obj_id, obj_data in objects.items():
        pos = obj_data['position']
        ori = obj_data['orientation']
        print(f"  {obj_id}: 位置({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f}), 朝向{ori}°")
    
    # 执行碰撞检测
    print(f"\n执行碰撞检测...")
    start_time = time.time()
    collisions = detector.check_collisions(objects)
    detection_time = time.time() - start_time
    
    print(f"检测结果:")
    print(f"  检测时间: {detection_time:.4f}s")
    print(f"  发现碰撞: {len(collisions)}个")
    
    for i, collision in enumerate(collisions):
        print(f"  碰撞 {i+1}:")
        print(f"    对象: {collision.object_a} <-> {collision.object_b}")
        print(f"    类型: {collision.collision_type.value}")
        print(f"    穿透深度: {collision.penetration_depth:.6f}m")
    
    # 检测LEGO连接
    print(f"\n检测LEGO螺柱连接...")
    connections = detector.detect_lego_connections(objects)
    print(f"发现连接: {len(connections)}个")
    
    for i, conn in enumerate(connections):
        print(f"  连接 {i+1}: {conn['brick_a']} <-> {conn['brick_b']}")
        print(f"    类型: {conn['connection_type']}, 强度: {conn['strength']}N")
    
    # 性能统计
    stats = detector.get_performance_stats()
    print(f"\n性能统计:")
    print(f"  总检测次数: {stats['total_detections']}")
    print(f"  平均检测时间: {stats['average_time']:.4f}s")
    print(f"  检测频率: {stats['detections_per_second']:.1f} Hz")
    
    print(f"✅ 碰撞检测演示完成")
    return collisions

def demo_stability_analysis():
    """演示稳定性分析"""
    print("\n⚖️ 演示稳定性分析")
    print("=" * 40)
    
    # 创建分析器
    analyzer = StabilityAnalyzer(threshold=0.7)
    
    # 创建测试城堡结构
    print("创建测试城堡结构 - 3层堆叠:")
    
    objects = {
        # 底层 - 稳定基础
        'L1_base_1': {
            'position': [0.484, -0.016, 0.065],
            'level': 1,
            'mass': 0.00253,
            'size': [0.0318, 0.0159, 0.0096]
        },
        'L1_base_2': {
            'position': [0.516, -0.016, 0.065],
            'level': 1,
            'mass': 0.00253,
            'size': [0.0318, 0.0159, 0.0096]
        },
        'L1_base_3': {
            'position': [0.484, 0.016, 0.065],
            'level': 1,
            'mass': 0.00253,
            'size': [0.0318, 0.0159, 0.0096]
        },
        'L1_base_4': {
            'position': [0.516, 0.016, 0.065],
            'level': 1,
            'mass': 0.00253,
            'size': [0.0318, 0.0159, 0.0096]
        },
        
        # 中层
        'L2_mid_1': {
            'position': [0.5, -0.008, 0.075],
            'level': 2,
            'mass': 0.00253,
            'size': [0.0318, 0.0159, 0.0096]
        },
        'L2_mid_2': {
            'position': [0.5, 0.008, 0.075],
            'level': 2,
            'mass': 0.00253,
            'size': [0.0318, 0.0159, 0.0096]
        },
        
        # 顶层 - 可能不稳定
        'L3_top_1': {
            'position': [0.505, 0.0, 0.085],  # 稍微偏心
            'level': 3,
            'mass': 0.00253,
            'size': [0.0159, 0.0159, 0.0096]  # 2x2积木
        }
    }
    
    for obj_id, obj_data in objects.items():
        pos = obj_data['position']
        level = obj_data['level']
        print(f"  {obj_id}: Level {level}, 位置({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f})")
    
    # 执行完整稳定性分析
    print(f"\n执行完整稳定性分析...")
    start_time = time.time()
    report = analyzer.analyze_complete_stability(objects)
    analysis_time = time.time() - start_time
    
    print(f"\n稳定性分析报告:")
    print(f"  分析时间: {analysis_time:.4f}s")
    print(f"  总体评分: {report.overall_score:.3f}")
    print(f"  总体评级: {report.overall_rating.value}")
    print(f"  是否稳定: {'是' if report.is_stable else '否'}")
    
    print(f"\n组件评分:")
    for component, score in report.component_scores.items():
        print(f"  {component}: {score:.3f}")
    
    print(f"\n重心分析:")
    com = report.com_analysis
    print(f"  系统重心: ({com.system_com[0]:.3f}, {com.system_com[1]:.3f}, {com.system_com[2]:.3f})")
    print(f"  重心在支撑内: {'是' if com.com_in_support else '否'}")
    print(f"  安全边距: {com.safety_margin:.3f}")
    
    print(f"\n支撑分析:")
    support = report.support_analysis
    print(f"  层数: {support.num_layers}")
    print(f"  最小支撑比例: {support.min_support_ratio:.3f}")
    print(f"  支撑评级: {support.support_rating.value}")
    
    print(f"\n力学分析:")
    force = report.force_analysis
    print(f"  总重量: {force.total_weight:.6f}N")
    print(f"  倾倒阻力: {force.tipping_resistance:.6f}N·m")
    print(f"  临界倾倒角: {force.critical_tipping_angle:.1f}°")
    
    print(f"\n几何分析:")
    geom = report.geometric_analysis
    print(f"  堆叠高度: {geom.stack_height:.3f}m")
    print(f"  基础宽度: {geom.base_width:.3f}m")
    print(f"  高宽比: {geom.aspect_ratio:.2f}")
    
    if report.risk_factors:
        print(f"\n⚠️ 风险因素:")
        for risk in report.risk_factors:
            print(f"  • {risk}")
    
    print(f"\n💡 建议:")
    for rec in report.recommendations:
        print(f"  • {rec}")
    
    print(f"✅ 稳定性分析演示完成")
    return report

def demo_visualization():
    """演示可视化系统"""
    print("\n🎨 演示可视化系统")
    print("=" * 40)
    
    # 创建可视化器
    visualizer = CastleVisualizer(real_time=False)
    print(f"可视化引擎: {visualizer.visualization_engine}")
    
    # 创建简化的城堡结构
    print("创建简化城堡结构进行可视化...")
    
    castle_bricks = [
        # 底层
        ("L1_base_1", [0.484, -0.016, 0.065], 0, "brick_2x4", "tan"),
        ("L1_base_2", [0.516, -0.016, 0.065], 0, "brick_2x4", "tan"),
        ("L1_base_3", [0.484, 0.016, 0.065], 0, "brick_2x4", "tan"),
        ("L1_base_4", [0.516, 0.016, 0.065], 0, "brick_2x4", "tan"),
        
        # 中层
        ("L2_tower_1", [0.492, -0.008, 0.075], 0, "brick_2x4", "tan"),
        ("L2_tower_2", [0.508, -0.008, 0.075], 0, "brick_2x4", "tan"),
        ("L2_tower_3", [0.492, 0.008, 0.075], 0, "brick_2x4", "tan"),
        ("L2_tower_4", [0.508, 0.008, 0.075], 0, "brick_2x4", "tan"),
        
        # 顶层圆锥
        ("L3_cone_1", [0.496, -0.004, 0.085], 0, "cone", "dark_gray"),
        ("L3_cone_2", [0.504, -0.004, 0.085], 0, "cone", "dark_gray"),
        ("L3_cone_3", [0.496, 0.004, 0.085], 0, "cone", "dark_gray"),
        ("L3_cone_4", [0.504, 0.004, 0.085], 0, "cone", "dark_gray"),
    ]
    
    # 逐步添加积木（模拟搭建过程）
    for i, (brick_id, position, orientation, brick_type, color) in enumerate(castle_bricks):
        print(f"  添加积木 {i+1}/{len(castle_bricks)}: {brick_id}")
        visualizer.add_brick(brick_id, position, orientation, brick_type, color)
        
        # 更新进度
        level = 1 if i < 4 else (2 if i < 8 else 3)
        visualizer.update_progress(level, i+1, len(castle_bricks))
        
        # 短暂暂停以显示搭建过程
        time.sleep(0.2)
    
    # 添加重心标记
    print("添加重心和支撑多边形...")
    visualizer.add_center_of_mass([0.5, 0, 0.075])
    
    # 添加支撑多边形
    support_polygon = np.array([
        [0.468, -0.024],
        [0.532, -0.024],
        [0.532, 0.024],
        [0.468, 0.024]
    ])
    visualizer.add_support_polygon(support_polygon)
    
    # 保存截图
    print("保存可视化截图...")
    visualizer.save_screenshot('demo_castle_visualization.png')
    
    print("✅ 可视化演示完成")
    print("   截图已保存: demo_castle_visualization.png")
    
    # 清理
    visualizer.close()
    return True

def demo_complete_system():
    """演示完整系统集成"""
    print("\n🔧 演示完整系统集成")
    print("=" * 40)
    
    # 创建配置
    from lego_castle_builder import BuildConfig
    
    config = BuildConfig(
        yumi_ip="127.0.0.1",
        enable_visualization=False,  # 演示模式禁用可视化
        placement_speed=0.1,
        stability_threshold=0.6
    )
    
    print("系统配置:")
    print(f"  YuMi IP: {config.yumi_ip}")
    print(f"  放置速度: {config.placement_speed} m/s")
    print(f"  稳定性阈值: {config.stability_threshold}")
    print(f"  积木质量: {config.brick_mass} kg")
    
    # 保存配置
    config_data = {
        'yumi_ip': config.yumi_ip,
        'left_arm_enabled': config.left_arm_enabled,
        'right_arm_enabled': config.right_arm_enabled,
        'placement_speed': config.placement_speed,
        'stability_threshold': config.stability_threshold,
        'brick_mass': config.brick_mass,
        'enable_visualization': config.enable_visualization
    }
    
    with open('demo_config.json', 'w') as f:
        json.dump(config_data, f, indent=2)
    
    print(f"\n✅ 系统配置已保存: demo_config.json")
    print(f"✅ 完整系统集成演示完成")
    
    return True

def main():
    """主演示函数"""
    print("🏰 LEGO城堡搭建系统演示")
    print("基于参考图片的精确8层城堡搭建")
    print("=" * 60)
    
    try:
        # 1. 城堡结构定义演示
        castle = demo_castle_structure()
        
        # 2. YuMi控制演示
        demo_yumi_control()
        
        # 3. 碰撞检测演示
        collisions = demo_collision_detection()
        
        # 4. 稳定性分析演示
        stability_report = demo_stability_analysis()
        
        # 5. 可视化演示
        demo_visualization()
        
        # 6. 完整系统演示
        demo_complete_system()
        
        # 总结
        print("\n" + "=" * 60)
        print("🎉 LEGO城堡搭建系统演示完成！")
        print("\n📊 演示结果总结:")
        print(f"  ✅ 城堡结构: {castle.get_total_brick_count()}个积木, {castle.get_level_count()}层")
        print(f"  ✅ YuMi控制: 双臂协调控制正常")
        print(f"  ✅ 碰撞检测: 检测到{len(collisions)}个碰撞")
        print(f"  ✅ 稳定性分析: 评分{stability_report.overall_score:.3f}, {stability_report.overall_rating.value}")
        print(f"  ✅ 可视化: matplotlib引擎正常工作")
        print(f"  ✅ 系统集成: 配置和接口正常")
        
        print("\n🚀 系统已准备就绪，可以开始真实的LEGO城堡搭建！")
        
        print("\n📋 下一步操作:")
        print("1. 安装完整依赖: pip install -r requirements.txt")
        print("2. 配置YuMi机械臂IP地址")
        print("3. 运行仿真测试: python run_castle_builder.py --simulation")
        print("4. 执行真实搭建: python run_castle_builder.py --real")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
